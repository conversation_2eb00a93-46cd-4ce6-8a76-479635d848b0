import mongoose, { Document, Schema } from 'mongoose';
import { CreateBranchInput } from '@/validation/schemas/education/college.schema';

export interface IBranch extends Omit<CreateBranchInput, 'degree'> {
  degree: mongoose.Types.ObjectId;
}

export interface BranchDocument extends IBranch, Document {
  createdAt: Date;
  updatedAt: Date;
}

const branchSchema = new Schema<BranchDocument>(
  {
    name: {
      type: String,
      required: [true, 'Branch name is required'],
      trim: true,
    },
    degree: {
      type: Schema.Types.ObjectId,
      ref: 'Degree',
      required: [true, 'Degree is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
branchSchema.index({ degree: 1, name: 1 }, { unique: true });

const Branch = mongoose.models.Branch || mongoose.model<BranchDocument>('Branch', branchSchema);

export default Branch;
