import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import ExamCategory from '@/models/exam/exam-category.model';
import Exam from '@/models/exam/exam.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createExamCategorySchema, UpdateExamCategoryInput } from '@/validation/schemas/education/exam.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const examCategoryAggregation: PipelineStage[] = [{ $sort: { name: 1 } }];

/**
 * Create a new exam category
 * @route POST /api/v1/education/exam-categories
 * @access Admin only
 */
export const createExamCategory = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createExamCategorySchema, req.body);
  const { name } = validatedData;

  const existingExamCategory = await ExamCategory.findOne({ name });
  if (existingExamCategory) {
    throw new BadRequestError(`An exam category with the name '${name}' already exists`);
  }

  const examCategory = await ExamCategory.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Exam category created successfully',
    data: { examCategory },
  });
};

/**
 * Get all exam categories
 * @route GET /api/v1/education/exam-categories
 * @access Public
 */
export const getAllExamCategories = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const baseAggregation = [...examCategoryAggregation];

  const queryManager = new AggregateQueryManager({
    model: ExamCategory,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [examCategories, pagination] = await Promise.all([
    queryManager.execute(),
    queryManager.getPaginationMetadata(),
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam categories fetched successfully',
    data: { examCategories, pagination },
  });
};

/**
 * Get a specific exam category by ID
 * @route GET /api/v1/education/exam-categories/:id
 * @access Public
 */
export const getExamCategoryById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam category ID');
  }

  const [examCategory] = await ExamCategory.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...examCategoryAggregation,
  ]);

  if (!examCategory) {
    throw new NotFoundError(`No exam category found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam category fetched successfully',
    data: { examCategory },
  });
};

/**
 * Update an exam category
 * @route PATCH /api/v1/education/exam-categories/:id
 * @access Admin only
 */
export const updateExamCategory = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateExamCategoryInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam category ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const examCategory = await ExamCategory.findById(id);
  if (!examCategory) {
    throw new NotFoundError(`No exam category found with id: ${id}`);
  }

  // Validate the update data
  const validatedData = await validateData(createExamCategorySchema.partial(), updateData);

  // Check for duplicate name
  if (validatedData.name) {
    const existingExamCategory = await ExamCategory.findOne({ name: validatedData.name, _id: { $ne: id } });
    if (existingExamCategory) {
      throw new BadRequestError(`An exam category with the name '${validatedData.name}' already exists`);
    }
  }

  await ExamCategory.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated exam category with aggregation
  const [updatedExamCategory] = await ExamCategory.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...examCategoryAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam category updated successfully',
    data: { examCategory: updatedExamCategory },
  });
};

/**
 * Delete an exam category
 * @route DELETE /api/v1/education/exam-categories/:id
 * @access Super Admin only
 */
export const deleteExamCategory = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam category ID');
  }

  const examCategory = await ExamCategory.findById(id);
  if (!examCategory) {
    throw new NotFoundError(`No exam category found with id: ${id}`);
  }

  // Check if there are any exams associated with this exam category
  const associatedExams = await Exam.findOne({ examCategory: id });
  if (associatedExams) {
    throw new BadRequestError(
      'Cannot delete this exam category because there are exams associated with it. Please delete those exams first.'
    );
  }

  await ExamCategory.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam category deleted successfully',
    data: null,
  });
};
