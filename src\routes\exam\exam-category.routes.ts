import express from 'express';
import {
  createExamCategory,
  getAllExamCategories,
  getExamCategoryById,
  updateExamCategory,
  deleteExamCategory,
} from '@/controllers/exam/exam-category.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllExamCategories);
router.get('/:id', getExamCategoryById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createExamCategory);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateExamCategory);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteExamCategory);

export default router;
