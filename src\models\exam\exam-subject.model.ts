import mongoose, { Document, Schema } from 'mongoose';
import { CreateExamSubjectInput } from '@/validation/schemas/education/exam.schema';

export interface IExamSubject extends Omit<CreateExamSubjectInput, 'exam'> {
  exam: mongoose.Types.ObjectId;
}

export interface ExamSubjectDocument extends IExamSubject, Document {
  createdAt: Date;
  updatedAt: Date;
}

const examSubjectSchema = new Schema<ExamSubjectDocument>(
  {
    name: {
      type: String,
      required: [true, 'Exam subject name is required'],
      trim: true,
    },
    exam: {
      type: Schema.Types.ObjectId,
      ref: 'Exam',
      required: [true, 'Exam is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
examSubjectSchema.index({ exam: 1, name: 1 }, { unique: true });

const ExamSubject = mongoose.models.ExamSubject || mongoose.model<ExamSubjectDocument>('ExamSubject', examSubjectSchema);

export default ExamSubject;
