import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Staff from '@/models/staff.model';
import StaffSession from '@/models/staff-session.model';
import { createStaffSessionUser, validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId } from 'mongoose';
import { CreateStaffInput, UpdateStaffInput, createStaffSchema, updateStaffSchema } from '@/validation/schemas/staff.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

export const createStaff = async (req: Request<{}, {}, CreateStaffInput>, res: Response): Promise<void> => {
  const validatedData = await validateData(createStaffSchema, req.body);
  const { email } = validatedData;

  const existingStaff = await Staff.findOne({ email });
  if (existingStaff) {
    throw new BadRequestError('A staff member with this email already exists');
  }

  const staff = await Staff.create(validatedData);

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Staff member created successfully',
    data: { id: staff._id, fullName: staff.fullName, email: staff.email, role: staff.role },
  });
};

export const getAllStaff = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const baseAggregation: any[] = [];

  const queryManager = new AggregateQueryManager({ model: Staff, queryString, baseAggregation }).filter().sort().paginate();

  const [staff, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Staff members fetched successfully',
    count: staff.length,
    data: { staff, pagination },
  });
};

export const getStaffById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!id || !isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid staff ID');
  }

  const aggregation = [{ $match: { _id: new mongoose.Types.ObjectId(id) } }, { $project: { password: 0 } }];

  const [staff] = await Staff.aggregate(aggregation);

  if (!staff) {
    throw new NotFoundError(`No staff found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Staff member fetched successfully',
    data: { staff },
  });
};

export const updateStaff = async (req: Request<{ id: string }, {}, UpdateStaffInput>, res: Response): Promise<void> => {
  const { id } = req.params;
  const validatedData = await validateData(updateStaffSchema, req.body);

  if (!id || !isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid staff ID');
  }

  const staff = await Staff.findByIdAndUpdate(id, validatedData, { new: true, runValidators: true }).select('-password');
  if (!staff) {
    throw new NotFoundError(`No staff found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Staff member updated successfully',
    data: { staff },
  });
};

export const deleteStaff = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!id || !isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid staff ID');
  }

  const staff = await Staff.findByIdAndDelete(id);
  if (!staff) {
    throw new NotFoundError(`No staff found with id: ${id}`);
  }

  await StaffSession.updateMany({ staff: id, isValid: true }, { isValid: false });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Staff member deleted successfully',
  });
};

export const getCurrentStaff = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const staffId = req.staff?.staffId;

  const staff = await Staff.findById(staffId);

  if (!staff) throw new NotFoundError('No staff found for the current user.');

  const sessionStaff = await createStaffSessionUser(staff);

  res.status(StatusCodes.OK).json({ success: true, message: 'Current staff fetched successfully', data: { staff: sessionStaff } });
};
