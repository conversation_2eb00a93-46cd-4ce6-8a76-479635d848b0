import express from 'express';
import { createHobbyType, getAllHobbyTypes, getHobbyTypeById, updateHobbyType, deleteHobbyType } from '@/controllers/hobby/hobby-type.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllHobbyTypes);
router.get('/:id', getHobbyTypeById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createHobbyType);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateHobbyType);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteHobbyType);

export default router;
