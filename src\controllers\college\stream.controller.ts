import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Stream from '@/models/college/stream.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createStreamSchema, UpdateStreamInput } from '@/validation/schemas/education/college.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const streamAggregation: PipelineStage[] = [{ $sort: { name: 1 } }];

/**
 * Create a new stream
 * @route POST /api/v1/education/streams
 * @access Private (Admin)
 */
export const createStream = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createStreamSchema, req.body);
  const { name } = validatedData;

  const existingStream = await Stream.findOne({ name });
  if (existingStream) {
    throw new BadRequestError(`A stream with the name '${name}' already exists`);
  }

  const stream = await Stream.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Stream created successfully',
    data: { stream },
  });
};

/**
 * Get all streams
 * @route GET /api/v1/education/streams
 * @access Public
 */
export const getAllStreams = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  const baseAggregation = [{ $match: matchStage }, ...streamAggregation];

  const queryManager = new AggregateQueryManager({
    model: Stream,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [streams, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Streams fetched successfully',
    data: { streams, pagination },
  });
};

/**
 * Get a stream by ID
 * @route GET /api/v1/education/streams/:id
 * @access Public
 */
export const getStreamById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid stream ID');
  }

  const stream = await Stream.findById(id);
  if (!stream) {
    throw new NotFoundError(`No stream found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Stream fetched successfully',
    data: { stream },
  });
};

/**
 * Update a stream
 * @route PATCH /api/v1/education/streams/:id
 * @access Private (Admin)
 */
export const updateStream = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateStreamInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid stream ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const stream = await Stream.findById(id);
  if (!stream) {
    throw new NotFoundError(`No stream found with id: ${id}`);
  }

  if (updateData.name && updateData.name !== stream.name) {
    const existingStream = await Stream.findOne({ name: updateData.name });
    if (existingStream) {
      throw new BadRequestError(`A stream with the name '${updateData.name}' already exists`);
    }
  }

  const updatedStream = await Stream.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Stream updated successfully',
    data: { stream: updatedStream },
  });
};

/**
 * Delete a stream
 * @route DELETE /api/v1/education/streams/:id
 * @access Private (Super Admin)
 */
export const deleteStream = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid stream ID');
  }

  const stream = await Stream.findById(id);
  if (!stream) {
    throw new NotFoundError(`No stream found with id: ${id}`);
  }

  // TODO: Check if there are any degrees associated with this stream
  // If yes, prevent deletion or implement cascade delete based on requirements

  await Stream.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Stream deleted successfully',
    data: null,
  });
};
