import mongoose, { Document, Schema } from 'mongoose';
import { CreateCollegeSubjectInput } from '@/validation/schemas/education/college.schema';

export interface ICollegeSubject extends Omit<CreateCollegeSubjectInput, 'branch'> {
  branch: mongoose.Types.ObjectId;
}

export interface CollegeSubjectDocument extends ICollegeSubject, Document {
  createdAt: Date;
  updatedAt: Date;
}

const collegeSubjectSchema = new Schema<CollegeSubjectDocument>(
  {
    name: {
      type: String,
      required: [true, 'Subject name is required'],
      trim: true,
    },
    branch: {
      type: Schema.Types.ObjectId,
      ref: 'Branch',
      required: [true, 'Branch is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
collegeSubjectSchema.index({ branch: 1, name: 1 }, { unique: true });

const CollegeSubject = mongoose.models.CollegeSubject || mongoose.model<CollegeSubjectDocument>('CollegeSubject', collegeSubjectSchema);

export default CollegeSubject;
