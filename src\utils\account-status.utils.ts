import { UserDocument } from '@/models/user.model';
import Address from '@/models/address.model';
import { IAccountStatusMap, accountStatusMap } from '@/validation/schemas/maps';

export const updateAccountStatus = async (
  user: UserDocument,
  options: {
    isVerified?: boolean;
    isProfileComplete?: boolean;
  }
): Promise<IAccountStatusMap> => {
  const { isVerified, isProfileComplete } = options;

  if (user.accountStatus === accountStatusMap.hold.key || user.accountStatus === accountStatusMap.blocked.key) {
    return user.accountStatus;
  }

  if (isVerified !== undefined) {
    if (isVerified) {
      user.accountStatus = accountStatusMap.incomplete.key as IAccountStatusMap;
    } else {
      user.accountStatus = accountStatusMap.pending.key as IAccountStatusMap;
    }
  }

  if (isProfileComplete !== undefined && isProfileComplete && user.accountStatus === accountStatusMap.incomplete.key) {
    user.accountStatus = accountStatusMap.active.key as IAccountStatusMap;
  }

  return user.accountStatus;
};

export const isProfileComplete = async (user: UserDocument): Promise<boolean> => {
  const hasBasicInfo = !!(user.fullName && user.email && user.dateOfBirth && user.location);

  if (!hasBasicInfo) return false;

  if (user.userType === 'student' || user.userType === 'tutor') {
    if (!user.gender) return false;

    if (!user.primaryWhatsApp && !user.phone) return false;
  }

  const addressCount = await Address.countDocuments({ user: user._id });

  return addressCount > 0;
};
