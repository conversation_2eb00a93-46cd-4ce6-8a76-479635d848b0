# Service Category API Documentation

## Overview

The Service Category API provides endpoints for managing service categories in the educational platform. Service categories represent the top-level educational domains such as Schools, Colleges, Languages, etc.

## Data Model

### Service Category

```typescript
{
  name: string; // enum: 'schools', 'colleges', 'languages', 'hobbies', 'it_courses', 'competitive_exams', 'entrance_exams'
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

## Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/service-categories` | Get all service categories | No |
| GET | `/service-categories/:id` | Get a specific service category | No |
| POST | `/service-categories` | Create a new service category | Yes (admin) |
| PATCH | `/service-categories/:id` | Update a service category | Yes (admin) |
| DELETE | `/service-categories/:id` | Delete a service category (not allowed) | Yes (super_admin) |

## Request/Response Examples

### GET /service-categories

**Response:**

```json
{
  "success": true,
  "message": "Service categories fetched successfully",
  "data": {
    "serviceCategories": [
      {
        "_id": "60a1b2c3d4e5f6a7b8c9d0e1",
        "name": "schools",
        "isActive": true,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      },
      {
        "_id": "60a1b2c3d4e5f6a7b8c9d0e2",
        "name": "colleges",
        "isActive": true,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
      // ... other service categories
    ],
    "pagination": {
      "totalDocs": 7,
      "limit": 10,
      "totalPages": 1,
      "page": 1,
      "hasPrevPage": false,
      "hasNextPage": false,
      "prevPage": null,
      "nextPage": null
    }
  }
}
```

### GET /service-categories/:id

**Response:**

```json
{
  "success": true,
  "message": "Service category fetched successfully",
  "data": {
    "serviceCategory": {
      "_id": "60a1b2c3d4e5f6a7b8c9d0e1",
      "name": "schools",
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

### POST /service-categories

**Request:**

```json
{
  "name": "schools",
  "isActive": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Service category created successfully",
  "data": {
    "serviceCategory": {
      "_id": "60a1b2c3d4e5f6a7b8c9d0e1",
      "name": "schools",
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

### PATCH /service-categories/:id

**Request:**

```json
{
  "isActive": false
}
```

**Response:**

```json
{
  "success": true,
  "message": "Service category updated successfully",
  "data": {
    "serviceCategory": {
      "_id": "60a1b2c3d4e5f6a7b8c9d0e1",
      "name": "schools",
      "isActive": false,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

### DELETE /service-categories/:id

**Response:**

```json
{
  "success": false,
  "message": "Service categories cannot be deleted as they are essential to the system. You can deactivate them instead using the update endpoint.",
  "error": "BadRequestError"
}
```

## Notes

- Service categories are essential to the system and cannot be deleted. They can only be deactivated by setting `isActive` to `false`.
- The name of a service category cannot be changed once it's created.
- Only administrators can create and update service categories.
- Only super administrators can attempt to delete service categories (which will result in an error).
