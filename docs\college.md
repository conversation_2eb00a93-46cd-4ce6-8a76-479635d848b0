# College Service Category Documentation

## Overview

The College service category is one of the seven main educational domains in the platform. It follows a hierarchical structure that allows for organizing higher education content in a structured manner.

## Hierarchy

The College service category follows a four-level hierarchy:

1. **Stream** - The broad field of study (e.g., Engineering, Medical, Arts)
2. **Degree Level** - The level of degree within a stream (e.g., Graduation, Post Graduation)
3. **Degree** - The specific degree program (e.g., B.Tech, M.Sc)
4. **Branch** - The specialization within a degree (e.g., Computer Science, Mechanical)
5. **Subject** - The academic subject taught within a branch (e.g., Data Structures, Thermodynamics)

```
College
├── Stream (Engineering, Medical, Arts, Commerce, Science, Management, Law, Other)
│   ├── Degree Level (Graduation, Post Graduation, Diploma, PhD)
│   │   ├── Degree (B.Tech, M.Sc, BBA, etc.)
│   │   │   ├── Branch (Computer Science, Mechanical, etc.)
│   │   │   │   ├── Subject (Data Structures, Thermodynamics, etc.)
```

Note: Each Degree Level is associated with a specific Stream, and each Degree is associated with a specific Degree Level.

## Data Models

### Stream Model

The Stream model represents broad fields of study in higher education.

```typescript
// src/models/college/stream.model.ts
export interface IStream {
  name: ICollegeStreamMap; // Enum: 'engineering', 'medical', 'arts', etc.
  isActive: boolean;
}

// Schema definition
const streamSchema = new Schema<StreamDocument>(
  {
    name: {
      type: String,
      enum: Object.keys(collegeStreamMap),
      required: [true, 'Stream name is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Index
streamSchema.index({ name: 1 });
```

### Degree Level Model

The Degree Level model represents the level of academic degrees within a specific stream.

```typescript
// src/models/college/degree-level.model.ts
export interface IDegreeLevel {
  name: string; // 'graduation', 'post_graduation', 'diploma', 'phd'
  stream: mongoose.Types.ObjectId; // Reference to Stream
  isActive: boolean;
}

// Schema definition
const degreeLevelSchema = new Schema<DegreeLevelDocument>(
  {
    name: {
      type: String,
      required: [true, 'Degree level name is required'],
      trim: true,
    },
    stream: {
      type: Schema.Types.ObjectId,
      ref: 'Stream',
      required: [true, 'Stream is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Index
degreeLevelSchema.index({ stream: 1, name: 1 }, { unique: true });
```

### Degree Model

The Degree model represents specific degree programs within a degree level.

```typescript
// src/models/college/degree.model.ts
export interface IDegree {
  name: string;
  degreeLevel: mongoose.Types.ObjectId; // Reference to DegreeLevel
  isActive: boolean;
}

// Schema definition
const degreeSchema = new Schema<DegreeDocument>(
  {
    name: {
      type: String,
      required: [true, 'Degree name is required'],
      trim: true,
    },
    degreeLevel: {
      type: Schema.Types.ObjectId,
      ref: 'DegreeLevel',
      required: [true, 'Degree level is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Index
degreeSchema.index({ degreeLevel: 1, name: 1 }, { unique: true });
```

### Branch Model

The Branch model represents specializations within a degree program.

```typescript
// src/models/college/branch.model.ts
export interface IBranch {
  name: string;
  degree: mongoose.Types.ObjectId; // Reference to Degree
  isActive: boolean;
}

// Schema definition
const branchSchema = new Schema<BranchDocument>(
  {
    name: {
      type: String,
      required: [true, 'Branch name is required'],
      trim: true,
    },
    degree: {
      type: Schema.Types.ObjectId,
      ref: 'Degree',
      required: [true, 'Degree is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Index
branchSchema.index({ degree: 1, name: 1 }, { unique: true });
```

### College Subject Model

The College Subject model represents academic subjects taught within a branch.

```typescript
// src/models/college/college-subject.model.ts
export interface ICollegeSubject {
  name: string;
  branch: mongoose.Types.ObjectId; // Reference to Branch
  isActive: boolean;
}

// Schema definition
const collegeSubjectSchema = new Schema<CollegeSubjectDocument>(
  {
    name: {
      type: String,
      required: [true, 'Subject name is required'],
      trim: true,
    },
    branch: {
      type: Schema.Types.ObjectId,
      ref: 'Branch',
      required: [true, 'Branch is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Index
collegeSubjectSchema.index({ branch: 1, name: 1 }, { unique: true });
```

## Predefined Data

### Streams

The system comes with predefined stream types:

- Engineering
- Medical
- Arts
- Commerce
- Science
- Management
- Law
- Other

### Degree Levels

The system comes with predefined degree levels:

- Graduation
- Post Graduation
- Diploma
- PhD

## Data Flow

1. **Creating a Stream**: Admin creates a stream (e.g., Engineering)
2. **Creating a Degree Level**: Admin creates a degree level for a specific stream (e.g., Graduation level for Engineering stream)
3. **Creating a Degree**: Admin creates a degree for a degree level (e.g., B.Tech for Graduation level in Engineering stream)
4. **Creating a Branch**: Admin creates a branch for a degree (e.g., Computer Science for B.Tech)
5. **Creating Subjects**: Admin creates subjects for a branch (e.g., Data Structures for Computer Science)

## Relationships with Other Entities

- **Users**: Students and tutors can be associated with specific streams, degrees, branches, and subjects
- **Content**: Educational content can be categorized by the college hierarchy
- **Assessments**: Tests and quizzes can be organized by the college hierarchy

## Implementation Notes

- The hierarchy is enforced through validation in the controllers
- When creating entities at lower levels, the parent entities must exist
- All entities have an `isActive` flag to enable/disable without deletion
