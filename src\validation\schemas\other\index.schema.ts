import { z } from 'zod';
import { businesLocationTypesMap, IBusinessLocationTypesMap } from './index.maps';
import { coordinatesSchema } from '../common.schema';

export const businessLocationSchema = z.object({
  name: z.string({ required_error: 'Business name is required' }).min(1, 'Business name is required'),
  location: z.string({ required_error: 'Location is required' }).min(1, 'Location is required'),
  type: z.enum(Object.keys(businesLocationTypesMap) as [IBusinessLocationTypesMap, ...IBusinessLocationTypesMap[]]).default('other'),
  coordinates: coordinatesSchema.optional(),
  isActive: z.boolean().default(true),
});

export type CreateBusinessLocationInput = z.infer<typeof businessLocationSchema>;
export type UpdateBusinessLocationInput = Partial<CreateBusinessLocationInput>;
