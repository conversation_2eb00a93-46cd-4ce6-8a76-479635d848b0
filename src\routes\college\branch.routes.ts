import express from 'express';
import { createBranch, getAllBranches, getBranchById, updateBranch, deleteBranch } from '@/controllers/college/branch.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllBranches);
router.get('/:id', getBranchById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createBranch);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateBranch);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteBranch);

export default router;
