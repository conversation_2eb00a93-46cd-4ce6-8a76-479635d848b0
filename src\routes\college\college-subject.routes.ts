import express from 'express';
import {
  createCollegeSubject,
  getAllCollegeSubjects,
  getCollegeSubjectById,
  updateCollegeSubject,
  deleteCollegeSubject,
} from '@/controllers/college/college-subject.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllCollegeSubjects);
router.get('/:id', getCollegeSubjectById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createCollegeSubject);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateCollegeSubject);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteCollegeSubject);

export default router;
