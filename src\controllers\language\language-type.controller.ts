import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import LanguageType from '@/models/language/language-type.model';
import Language from '@/models/language/language.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createLanguageTypeSchema, UpdateLanguageTypeInput } from '@/validation/schemas/education/language.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const languageTypeAggregation: PipelineStage[] = [{ $sort: { name: 1 } }];

/**
 * Create a new language type
 * @route POST /api/v1/education/language-types
 * @access Admin only
 */
export const createLanguageType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createLanguageTypeSchema, req.body);
  const { name } = validatedData;

  const existingLanguageType = await LanguageType.findOne({ name });
  if (existingLanguageType) {
    throw new BadRequestError(`A language type with the name '${name}' already exists`);
  }

  const languageType = await LanguageType.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Language type created successfully',
    data: { languageType },
  });
};

/**
 * Get all language types
 * @route GET /api/v1/education/language-types
 * @access Public
 */
export const getAllLanguageTypes = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const baseAggregation = [...languageTypeAggregation];

  const queryManager = new AggregateQueryManager({
    model: LanguageType,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [languageTypes, pagination] = await Promise.all([
    queryManager.execute(),
    queryManager.getPaginationMetadata(),
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Language types fetched successfully',
    data: { languageTypes, pagination },
  });
};

/**
 * Get a specific language type by ID
 * @route GET /api/v1/education/language-types/:id
 * @access Public
 */
export const getLanguageTypeById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid language type ID');
  }

  const [languageType] = await LanguageType.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...languageTypeAggregation,
  ]);

  if (!languageType) {
    throw new NotFoundError(`No language type found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Language type fetched successfully',
    data: { languageType },
  });
};

/**
 * Update a language type
 * @route PATCH /api/v1/education/language-types/:id
 * @access Admin only
 */
export const updateLanguageType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateLanguageTypeInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid language type ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const languageType = await LanguageType.findById(id);
  if (!languageType) {
    throw new NotFoundError(`No language type found with id: ${id}`);
  }

  // Validate the update data
  const validatedData = await validateData(createLanguageTypeSchema.partial(), updateData);

  // Check for duplicate name
  if (validatedData.name) {
    const existingLanguageType = await LanguageType.findOne({ name: validatedData.name, _id: { $ne: id } });
    if (existingLanguageType) {
      throw new BadRequestError(`A language type with the name '${validatedData.name}' already exists`);
    }
  }

  await LanguageType.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated language type with aggregation
  const [updatedLanguageType] = await LanguageType.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...languageTypeAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Language type updated successfully',
    data: { languageType: updatedLanguageType },
  });
};

/**
 * Delete a language type
 * @route DELETE /api/v1/education/language-types/:id
 * @access Super Admin only
 */
export const deleteLanguageType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid language type ID');
  }

  const languageType = await LanguageType.findById(id);
  if (!languageType) {
    throw new NotFoundError(`No language type found with id: ${id}`);
  }

  // Check if there are any languages associated with this language type
  const associatedLanguages = await Language.findOne({ languageType: id });
  if (associatedLanguages) {
    throw new BadRequestError(
      'Cannot delete this language type because there are languages associated with it. Please delete those languages first.'
    );
  }

  await LanguageType.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Language type deleted successfully',
    data: null,
  });
};
