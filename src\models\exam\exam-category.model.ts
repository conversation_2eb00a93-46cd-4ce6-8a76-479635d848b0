import mongoose, { Document, Schema } from 'mongoose';
import { CreateExamCategoryInput } from '@/validation/schemas/education/exam.schema';

export interface IExamCategory extends CreateExamCategoryInput {}

export interface ExamCategoryDocument extends IExamCategory, Document {
  createdAt: Date;
  updatedAt: Date;
}

const examCategorySchema = new Schema<ExamCategoryDocument>(
  {
    name: {
      type: String,
      required: [true, 'Exam category name is required'],
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
examCategorySchema.index({ name: 1 });

const ExamCategory = mongoose.models.ExamCategory || mongoose.model<ExamCategoryDocument>('ExamCategory', examCategorySchema);

export default ExamCategory;
