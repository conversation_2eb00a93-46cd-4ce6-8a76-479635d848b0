import express from 'express';
import {
  createExamSubject,
  getAllExamSubjects,
  getExamSubjectsByExam,
  getExamSubjectById,
  updateExamSubject,
  deleteExamSubject,
} from '@/controllers/exam/exam-subject.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllExamSubjects);
router.get('/exam/:examId', getExamSubjectsByExam);
router.get('/:id', getExamSubjectById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createExamSubject);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateExamSubject);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteExamSubject);

export default router;
