# Languages Service Category Documentation

## Overview

The Languages service category is one of the seven main educational domains in the platform. It provides a structured way to organize language learning content and services.

## Hierarchy

The Languages service category follows a two-level hierarchy:

1. **Language Type** - The category of language (e.g., Indian, Foreign)
2. **Language** - The specific language within a category (e.g., Hindi, English, French)

```
Languages
├── Language Type (Indian, Foreign)
│   ├── Language (Hindi, English, French, German, etc.)
```

## Data Models

### Language Type Model

The Language Type model represents categories of languages.

```typescript
// src/models/language/language-type.model.ts
export interface ILanguageType {
  name: ILanguageTypeMap; // Enum: 'indian', 'foreign'
  isActive: boolean;
}

// Schema definition
const languageTypeSchema = new Schema<LanguageTypeDocument>({
  name: {
    type: String,
    enum: Object.keys(languageTypeMap),
    required: [true, 'Language type name is required'],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
languageTypeSchema.index({ name: 1 });
```

### Language Model

The Language model represents specific languages within a language type.

```typescript
// src/models/language/language.model.ts
export interface ILanguage {
  name: string;
  languageType: mongoose.Types.ObjectId; // Reference to LanguageType
  isActive: boolean;
}

// Schema definition
const languageSchema = new Schema<LanguageDocument>({
  name: {
    type: String,
    required: [true, 'Language name is required'],
    trim: true,
  },
  languageType: {
    type: Schema.Types.ObjectId,
    ref: 'LanguageType',
    required: [true, 'Language type is required'],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
languageSchema.index({ languageType: 1, name: 1 }, { unique: true });
```

## API Endpoints

### Language Type Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/language-types` | Get all language types | No |
| GET | `/api/v1/education/language-types/:id` | Get a specific language type | No |
| POST | `/api/v1/education/language-types` | Create a new language type | Yes (admin) |
| PATCH | `/api/v1/education/language-types/:id` | Update a language type | Yes (admin) |
| DELETE | `/api/v1/education/language-types/:id` | Delete a language type | Yes (super_admin) |

### Language Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/languages` | Get all languages | No |
| GET | `/api/v1/education/languages/type/:languageTypeId` | Get languages by type | No |
| GET | `/api/v1/education/languages/:id` | Get a specific language | No |
| POST | `/api/v1/education/languages` | Create a new language | Yes (admin) |
| PATCH | `/api/v1/education/languages/:id` | Update a language | Yes (admin) |
| DELETE | `/api/v1/education/languages/:id` | Delete a language | Yes (super_admin) |

## Predefined Data

### Language Types

The system comes with predefined language types:

- Indian
- Foreign

### Example Languages

#### Indian Languages
- Hindi
- Bengali
- Tamil
- Telugu
- Marathi
- Gujarati
- Kannada
- Malayalam
- Punjabi
- Urdu

#### Foreign Languages
- English
- French
- German
- Spanish
- Japanese
- Chinese
- Russian
- Arabic
- Portuguese
- Italian

## Data Flow

1. **Creating a Language Type**: Admin creates a language type (e.g., Indian)
2. **Creating Languages**: Admin creates languages within that type (e.g., Hindi, Tamil, Telugu)

## Usage Examples

### Creating a Language Type

```javascript
// POST /api/v1/education/language-types
{
  "name": "indian"
}
```

### Creating a Language

```javascript
// POST /api/v1/education/languages
{
  "name": "Hindi",
  "languageType": "60d5ec9af682d123e4567890" // Language Type ID
}
```

### Getting Languages by Type

```
GET /api/v1/education/languages/type/60d5ec9af682d123e4567890
```

## Relationships with Other Entities

- **Users**: Tutors can specify which languages they teach
- **Students**: Students can express interest in learning specific languages
- **Content**: Educational content can be categorized by language
- **Classes/Courses**: Language classes and courses can be organized by language

## Implementation Notes

- The hierarchy is enforced through validation in the controllers
- When creating a language, the language type must exist
- All entities have an `isActive` flag to enable/disable without deletion
- The platform supports multilingual content based on these language definitions
