import express from 'express';
import {
  createCourse,
  getAllCourses,
  getCoursesByType,
  getCourseById,
  updateCourse,
  deleteCourse,
} from '@/controllers/course/course.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllCourses);
router.get('/type/:courseTypeId', getCoursesByType);
router.get('/:id', getCourseById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createCourse);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateCourse);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteCourse);

export default router;
