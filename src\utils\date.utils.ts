import {
  format,
  parse,
  parseISO,
  isValid,
  addDays,
  addMonths,
  addYears,
  differenceInDays,
  differenceInMonths,
  differenceInYears,
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  isAfter,
  isBefore,
  formatDistance,
} from 'date-fns';
import { colorizer } from '@/utils';

/**
 * Format a date to a string using the specified format
 * @param date The date to format
 * @param formatStr The format string (default: 'yyyy-MM-dd')
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string | number, formatStr = 'yyyy-MM-dd'): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    if (!isValid(dateObj)) {
      throw new Error('Invalid date');
    }
    return format(dateObj, formatStr);
  } catch (error) {
    colorizer.error(`Error formatting date: ${error}`);
    return '';
  }
};

/**
 * Parse a date string to a Date object
 * @param dateStr The date string to parse
 * @param formatStr The format of the date string
 * @returns Parsed Date object or null if invalid
 */
export const parseDate = (dateStr: string, formatStr = 'yyyy-MM-dd'): Date | null => {
  try {
    const parsedDate = parse(dateStr, formatStr, new Date());
    return isValid(parsedDate) ? parsedDate : null;
  } catch (error) {
    colorizer.error(`Error parsing date: ${error}`);
    return null;
  }
};

/**
 * Get the current date formatted as YYYY-MM-DD
 * @returns Current date as YYYY-MM-DD
 */
export const getCurrentDate = (): string => {
  return formatDate(new Date());
};

/**
 * Get the current time formatted as HH:MM AM/PM
 * @returns Current time as HH:MM AM/PM
 */
export const getCurrentTime = (): string => {
  return format(new Date(), 'h:mm a');
};

/**
 * Get the current date and time as an object with date and time properties
 * @returns Object with date (YYYY-MM-DD) and time (HH:MM AM/PM)
 */
export const getCurrentDateTime = (): { date: string; time: string } => {
  const now = new Date();
  return {
    date: formatDate(now),
    time: format(now, 'h:mm a'),
  };
};

/**
 * Format a date for display in the UI
 * @param date The date to format
 * @param includeTime Whether to include the time
 * @returns Formatted date string
 */
export const formatDateForDisplay = (date: Date | string | number, includeTime = false): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    if (!isValid(dateObj)) {
      throw new Error('Invalid date');
    }
    
    return includeTime 
      ? format(dateObj, 'MMM d, yyyy h:mm a')
      : format(dateObj, 'MMM d, yyyy');
  } catch (error) {
    colorizer.error(`Error formatting date for display: ${error}`);
    return '';
  }
};

/**
 * Format a date as a relative time (e.g., "2 days ago")
 * @param date The date to format
 * @param baseDate The base date to compare against (default: now)
 * @returns Relative time string
 */
export const formatRelativeTime = (date: Date | string | number, baseDate = new Date()): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    if (!isValid(dateObj)) {
      throw new Error('Invalid date');
    }
    
    return formatDistance(dateObj, baseDate, { addSuffix: true });
  } catch (error) {
    colorizer.error(`Error formatting relative time: ${error}`);
    return '';
  }
};

/**
 * Get the start of the day for a given date
 * @param date The date
 * @returns Date object representing the start of the day
 */
export const getStartOfDay = (date: Date | string | number): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
  return startOfDay(dateObj);
};

/**
 * Get the end of the day for a given date
 * @param date The date
 * @returns Date object representing the end of the day
 */
export const getEndOfDay = (date: Date | string | number): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
  return endOfDay(dateObj);
};

/**
 * Add days to a date
 * @param date The date
 * @param amount Number of days to add
 * @returns New date with days added
 */
export const addDaysToDate = (date: Date | string | number, amount: number): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
  return addDays(dateObj, amount);
};

/**
 * Add months to a date
 * @param date The date
 * @param amount Number of months to add
 * @returns New date with months added
 */
export const addMonthsToDate = (date: Date | string | number, amount: number): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
  return addMonths(dateObj, amount);
};

/**
 * Add years to a date
 * @param date The date
 * @param amount Number of years to add
 * @returns New date with years added
 */
export const addYearsToDate = (date: Date | string | number, amount: number): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
  return addYears(dateObj, amount);
};

/**
 * Calculate age from date of birth
 * @param dateOfBirth Date of birth
 * @returns Age in years
 */
export const calculateAge = (dateOfBirth: Date | string | number): number => {
  try {
    const dob = typeof dateOfBirth === 'string' ? parseISO(dateOfBirth) : new Date(dateOfBirth);
    if (!isValid(dob)) {
      throw new Error('Invalid date of birth');
    }
    
    return differenceInYears(new Date(), dob);
  } catch (error) {
    colorizer.error(`Error calculating age: ${error}`);
    return 0;
  }
};

/**
 * Calculate detailed age with years, months, and days
 * @param dateOfBirth Date of birth
 * @returns Object with years, months, and days
 */
export const calculateDetailedAge = (dateOfBirth: Date | string | number): { years: number; months: number; days: number } => {
  try {
    const dob = typeof dateOfBirth === 'string' ? parseISO(dateOfBirth) : new Date(dateOfBirth);
    if (!isValid(dob)) {
      throw new Error('Invalid date of birth');
    }
    
    const now = new Date();
    const years = differenceInYears(now, dob);
    const dateWithAddedYears = addYears(dob, years);
    
    const months = differenceInMonths(now, dateWithAddedYears);
    const dateWithAddedMonths = addMonths(dateWithAddedYears, months);
    
    const days = differenceInDays(now, dateWithAddedMonths);
    
    return { years, months, days };
  } catch (error) {
    colorizer.error(`Error calculating detailed age: ${error}`);
    return { years: 0, months: 0, days: 0 };
  }
};

/**
 * Check if a date is in the past
 * @param date The date to check
 * @returns True if the date is in the past
 */
export const isDateInPast = (date: Date | string | number): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    if (!isValid(dateObj)) {
      throw new Error('Invalid date');
    }
    
    return isBefore(dateObj, new Date());
  } catch (error) {
    colorizer.error(`Error checking if date is in past: ${error}`);
    return false;
  }
};

/**
 * Check if a date is in the future
 * @param date The date to check
 * @returns True if the date is in the future
 */
export const isDateInFuture = (date: Date | string | number): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    if (!isValid(dateObj)) {
      throw new Error('Invalid date');
    }
    
    return isAfter(dateObj, new Date());
  } catch (error) {
    colorizer.error(`Error checking if date is in future: ${error}`);
    return false;
  }
};

/**
 * Get date range for a specific period
 * @param period The period ('day', 'month', 'year')
 * @param date The reference date (default: today)
 * @returns Object with start and end dates
 */
export const getDateRange = (
  period: 'day' | 'month' | 'year',
  date: Date | string | number = new Date()
): { start: Date; end: Date } => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    if (!isValid(dateObj)) {
      throw new Error('Invalid date');
    }
    
    switch (period) {
      case 'day':
        return {
          start: startOfDay(dateObj),
          end: endOfDay(dateObj),
        };
      case 'month':
        return {
          start: startOfMonth(dateObj),
          end: endOfMonth(dateObj),
        };
      case 'year':
        return {
          start: startOfYear(dateObj),
          end: endOfYear(dateObj),
        };
      default:
        throw new Error('Invalid period');
    }
  } catch (error) {
    colorizer.error(`Error getting date range: ${error}`);
    return {
      start: new Date(),
      end: new Date(),
    };
  }
};

/**
 * Format a date for use in a filename (YYYYMMDD-HHMMSS)
 * @param date The date to format
 * @returns Formatted date string for filenames
 */
export const formatDateForFilename = (date: Date | string | number = new Date()): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);
    if (!isValid(dateObj)) {
      throw new Error('Invalid date');
    }
    
    return format(dateObj, 'yyyyMMdd-HHmmss');
  } catch (error) {
    colorizer.error(`Error formatting date for filename: ${error}`);
    return format(new Date(), 'yyyyMMdd-HHmmss');
  }
};

/**
 * Generate a timestamp for use in IDs or filenames
 * @returns Numeric timestamp
 */
export const generateTimestamp = (): number => {
  return Date.now();
};
