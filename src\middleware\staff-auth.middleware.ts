import type { Response, NextFunction } from 'express';
import { UnauthenticatedError, UnauthorizedError } from '@/errors';
import { validateStaffSession, ISessionStaff } from '@/utils/staff-auth.utils';
import StaffSession from '@/models/staff-session.model';
import type { AuthenticatedStaffRequest } from '@/types/express';

export const authenticatedStaff = async (req: AuthenticatedStaffRequest, res: Response, next: NextFunction): Promise<void> => {
  const cookieToken = req.signedCookies.staff_session;

  try {
    if (!cookieToken) throw new UnauthenticatedError('Authentication required - no valid session found');

    const payload = await validateStaffSession(cookieToken);

    if (payload.sessionId) {
      const dbSession = await StaffSession.findOne({
        sessionId: payload.sessionId,
        staff: payload.staff.staffId,
        isValid: true,
      });

      if (!dbSession) {
        throw new UnauthenticatedError('Session has been invalidated');
      }

      if (dbSession.expiresAt < new Date()) {
        throw new UnauthenticatedError('Session has expired');
      }
    }

    req.staff = payload.staff;
    req.sessionId = payload.sessionId;

    next();
  } catch (err) {
    console.error('Staff authentication error:', err);

    res.cookie('staff_session', 'logout', { httpOnly: true, expires: new Date(Date.now()) });

    throw new UnauthenticatedError('Authentication failed - please log in again');
  }
};

export const authorizeStaffRoles = (...allowedRoles: string[]) => {
  return (req: AuthenticatedStaffRequest, _res: Response, next: NextFunction): void => {
    if (!req.staff) throw new UnauthenticatedError('Authentication required');

    if (typeof req.staff.role === 'string' && allowedRoles.includes(req.staff.role)) {
      return next();
    }

    throw new UnauthorizedError(`Not authorized - requires one of these roles: ${allowedRoles.join(', ')}`);
  };
};

export const checkStaffPermissions = (requestStaff: ISessionStaff, resourceStaffId: string): void => {
  if (requestStaff.role === 'super_admin') {
    return;
  }

  if (requestStaff.role === 'admin') {
    return;
  }

  const resourceId = resourceStaffId.toString();
  if (requestStaff.staffId === resourceId) {
    return;
  }

  throw new UnauthorizedError('Not authorized to access this resource');
};
