import mongoose, { Document, Schema } from 'mongoose';
import validator from 'validator';
import bcrypt from 'bcryptjs';
import { REGEX_MAP } from '@/constants';
import { adminRolesMap } from '@/validation/schemas/maps';
import { CreateStaffInput } from '@/validation/schemas/staff.schema';

export interface IStaff extends Omit<CreateStaffInput, 'confirmPassword'> {
  lastLogin?: Date;
  permissions?: string[];
}

export interface StaffDocument extends IStaff, Document<mongoose.Types.ObjectId> {
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const staffSchema = new Schema<StaffDocument>(
  {
    fullName: {
      type: String,
      required: [true, 'Please enter your full name'],
      trim: true,
      minlength: [2, 'Full name must be at least 2 characters long'],
      maxlength: [60, 'Full name cannot exceed 60 characters'],
      validate: {
        validator: function (v: string) {
          return REGEX_MAP.ALPHABETS.test(v);
        },
        message: 'Full name must contain only alphanumeric characters',
      },
    },
    email: {
      type: String,
      required: [true, 'Please enter your email address'],
      unique: true,
      trim: true,
      lowercase: true,
      validate: [validator.isEmail, 'Please enter a valid email address'],
    },
    phone: {
      type: String,
      trim: true,
      validate: {
        validator: function (v: string) {
          return REGEX_MAP.PHONE.test(v);
        },
        message: 'Please enter a valid phone number',
      },
    },
    password: {
      type: String,
      required: [true, 'Please enter a password'],
      minlength: [8, 'Password must be at least 8 characters long'],
      select: false,
    },
    role: {
      type: String,
      enum: Object.keys(adminRolesMap),
      required: [true, 'Staff role is required'],
    },
    profilePicture: {
      type: String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    lastLogin: {
      type: Date,
    },
    permissions: {
      type: [String],
      default: [],
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

staffSchema.pre('save', async function (this: StaffDocument) {
  if (!this.isModified('password')) return;
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

staffSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  return await bcrypt.compare(candidatePassword, this.password);
};

const Staff = mongoose.model<StaffDocument>('Staff', staffSchema);

export default Staff;
