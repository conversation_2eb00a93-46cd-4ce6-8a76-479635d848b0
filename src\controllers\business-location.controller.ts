import { Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError } from '@/errors';
import BusinessLocation from '@/models/business-location.model';
import { AuthenticatedRequest } from '@/types/express';

export const searchBusinessLocations = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const { query } = req.query;

  if (!query || typeof query !== 'string') {
    throw new BadRequestError('Please provide a valid search query');
  }

  const searchQuery = query.toLowerCase().trim();

  const businessLocations = await BusinessLocation.find({
    $or: [{ name: { $regex: searchQuery, $options: 'i' } }, { location: { $regex: searchQuery, $options: 'i' } }],
    isActive: true,
  })
    .sort({ name: 1 })
    .limit(10)
    .lean();

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Business locations searched successfully',
    data: { businessLocations },
  });
};
