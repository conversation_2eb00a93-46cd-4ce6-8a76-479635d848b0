import { z } from 'zod';
import { objectIdSchema } from '../common.schema';

// College Stream Schemas
export const createStreamSchema = z.object({
  name: z.string({ required_error: 'Stream name is required' }).min(1, 'Stream name is required'),
  isActive: z.boolean().default(true),
});

export type CreateStreamInput = z.infer<typeof createStreamSchema>;
export type UpdateStreamInput = Partial<CreateStreamInput>;

// Degree Level Schemas
export const createDegreeLevelSchema = z.object({
  name: z.string({ required_error: 'Degree level name is required' }).min(1, 'Degree level name is required'),
  stream: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateDegreeLevelInput = z.infer<typeof createDegreeLevelSchema>;
export type UpdateDegreeLevelInput = Partial<CreateDegreeLevelInput>;

// Degree Schemas
export const createDegreeSchema = z.object({
  name: z.string({ required_error: 'Degree name is required' }).min(1, 'Degree name is required'),
  degreeLevel: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateDegreeInput = z.infer<typeof createDegreeSchema>;
export type UpdateDegreeInput = Partial<CreateDegreeInput>;

// Branch Schemas
export const createBranchSchema = z.object({
  name: z.string({ required_error: 'Branch name is required' }).min(1, 'Branch name is required'),
  degree: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateBranchInput = z.infer<typeof createBranchSchema>;
export type UpdateBranchInput = Partial<CreateBranchInput>;

// College Subject Schemas
export const createCollegeSubjectSchema = z.object({
  name: z.string({ required_error: 'Subject name is required' }).min(1, 'Subject name is required'),
  branch: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateCollegeSubjectInput = z.infer<typeof createCollegeSubjectSchema>;
export type UpdateCollegeSubjectInput = Partial<CreateCollegeSubjectInput>;
