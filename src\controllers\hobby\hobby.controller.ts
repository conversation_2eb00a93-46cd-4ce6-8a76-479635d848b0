import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Hobby from '@/models/hobby/hobby.model';
import HobbyType from '@/models/hobby/hobby-type.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createHobbySchema, UpdateHobbyInput } from '@/validation/schemas/education/hobby.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const hobbyAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'hobbytypes',
      localField: 'hobbyType',
      foreignField: '_id',
      as: 'hobbyTypeDetails',
    },
  },
  { $unwind: { path: '$hobbyTypeDetails', preserveNullAndEmptyArrays: true } },
  { $sort: { 'hobbyTypeDetails.name': 1, name: 1 } },
];

/**
 * Create a new hobby
 * @route POST /api/v1/education/hobbies
 * @access Admin only
 */
export const createHobby = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createHobbySchema, req.body);
  const { name, hobbyType } = validatedData;

  if (!isValidObjectId(hobbyType)) {
    throw new BadRequestError('Please provide a valid hobby type ID');
  }

  const hobbyTypeExists = await HobbyType.findById(hobbyType);
  if (!hobbyTypeExists) {
    throw new NotFoundError(`No hobby type found with id: ${hobbyType}`);
  }

  const existingHobby = await Hobby.findOne({ name, hobbyType });
  if (existingHobby) {
    throw new BadRequestError(`A hobby with the name '${name}' already exists for this hobby type`);
  }

  const hobby = await Hobby.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Hobby created successfully',
    data: { hobby },
  });
};

/**
 * Get all hobbies
 * @route GET /api/v1/education/hobbies
 * @access Public
 */
export const getAllHobbies = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.hobbyType && isValidObjectId(queryString.hobbyType)) {
    matchStage['hobbyType'] = new mongoose.Types.ObjectId(queryString.hobbyType as string);
    delete queryString.hobbyType;
  }

  const baseAggregation = [{ $match: matchStage }, ...hobbyAggregation];

  const queryManager = new AggregateQueryManager({
    model: Hobby,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [hobbies, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Hobbies fetched successfully',
    data: { hobbies, pagination },
  });
};

/**
 * Get a specific hobby by ID
 * @route GET /api/v1/education/hobbies/:id
 * @access Public
 */
export const getHobbyById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid hobby ID');
  }

  const [hobby] = await Hobby.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...hobbyAggregation]);

  if (!hobby) {
    throw new NotFoundError(`No hobby found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Hobby fetched successfully',
    data: { hobby },
  });
};

/**
 * Update a hobby
 * @route PATCH /api/v1/education/hobbies/:id
 * @access Admin only
 */
export const updateHobby = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateHobbyInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid hobby ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const hobby = await Hobby.findById(id);
  if (!hobby) {
    throw new NotFoundError(`No hobby found with id: ${id}`);
  }

  // Validate hobby type if provided
  if (updateData.hobbyType && !isValidObjectId(updateData.hobbyType)) {
    throw new BadRequestError('Please provide a valid hobby type ID');
  }

  if (updateData.hobbyType) {
    const hobbyTypeExists = await HobbyType.findById(updateData.hobbyType);
    if (!hobbyTypeExists) {
      throw new NotFoundError(`No hobby type found with id: ${updateData.hobbyType}`);
    }
  }

  // Validate the update data
  const validatedData = await validateData(createHobbySchema.partial(), updateData);

  // Check for duplicate name within the same hobby type
  if (validatedData.name || validatedData.hobbyType) {
    const hobbyTypeId = validatedData.hobbyType || hobby.hobbyType;
    const hobbyName = validatedData.name || hobby.name;

    const existingHobby = await Hobby.findOne({
      name: hobbyName,
      hobbyType: hobbyTypeId,
      _id: { $ne: id },
    });

    if (existingHobby) {
      throw new BadRequestError(`A hobby with the name '${hobbyName}' already exists for this hobby type`);
    }
  }

  await Hobby.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated hobby with aggregation
  const [updatedHobby] = await Hobby.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...hobbyAggregation]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Hobby updated successfully',
    data: { hobby: updatedHobby },
  });
};

/**
 * Delete a hobby
 * @route DELETE /api/v1/education/hobbies/:id
 * @access Super Admin only
 */
export const deleteHobby = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid hobby ID');
  }

  const hobby = await Hobby.findById(id);
  if (!hobby) {
    throw new NotFoundError(`No hobby found with id: ${id}`);
  }

  // TODO: Check if there are any references to this hobby in other collections
  // If yes, prevent deletion or implement cascade delete based on requirements

  await Hobby.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Hobby deleted successfully',
    data: null,
  });
};
