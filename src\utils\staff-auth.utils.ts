import jwt from 'jsonwebtoken';
import type { Response } from 'express';
import { UnauthenticatedError } from '@/errors';
import { StaffDocument } from '@/models/staff.model';
import { IAdminRolesMap } from '@/validation/schemas/maps';

export interface ISessionStaff {
  staffId: string;
  fullName: string;
  email: string;
  role: IAdminRolesMap;
  profilePicture?: string;
  phone?: string;
  isActive: boolean;
}

export interface StaffJWTPayload {
  staff: ISessionStaff;
  sessionId?: string;
  iat?: number;
  exp?: number;
}

export interface StaffTokenPayload {
  staff: ISessionStaff;
  sessionId?: string;
}

interface CookieOptions {
  httpOnly: boolean;
  secure: boolean;
  signed: boolean;
  sameSite: 'strict' | 'lax' | 'none';
  maxAge?: number;
  domain?: string;
  path?: string;
  expires?: Date;
}

export const createStaffSessionUser = async (staff: StaffDocument): Promise<ISessionStaff> => {
  return {
    staffId: staff._id.toString(),
    fullName: staff.fullName,
    email: staff.email,
    role: staff.role,
    profilePicture: staff.profilePicture,
    phone: staff.phone,
    isActive: staff.isActive,
  };
};

export const createStaffToken = ({ payload, expiresIn }: { payload: StaffTokenPayload; expiresIn: string }): string => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  const secret = process.env.JWT_SECRET as jwt.Secret;
  return jwt.sign(payload, secret, {
    expiresIn,
    algorithm: 'HS256',
    issuer: 'perfecttutor-api',
  } as jwt.SignOptions);
};

export const validateStaffSession = async (token: string): Promise<StaffJWTPayload> => {
  try {
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }

    const secret = process.env.JWT_SECRET as jwt.Secret;
    const payload = jwt.verify(token, secret, {
      algorithms: ['HS256'],
      issuer: 'perfecttutor-api',
    }) as StaffJWTPayload;

    if (!payload.staff || !payload.staff.staffId || !payload.staff.role) {
      throw new Error('Session payload is missing required fields');
    }

    return payload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new UnauthenticatedError('Session expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new UnauthenticatedError('Invalid session');
    }
    throw new UnauthenticatedError('Authentication invalid');
  }
};

export const createStaffSessionCookie = async ({
  res,
  staff,
  sessionId,
  returnTokenOnly = false,
}: {
  res: Response;
  staff: ISessionStaff;
  sessionId: string;
  returnTokenOnly?: boolean;
}): Promise<string> => {
  try {
    const sessionLifetime = process.env.SESSION_LIFETIME || '7d';
    const domain = process.env.APP_DOMAIN || '.perfecttutor.site';

    const sessionToken = createStaffToken({
      payload: { staff, sessionId },
      expiresIn: sessionLifetime,
    });

    if (!returnTokenOnly) {
      const sessionMaxAge = Number.parseInt(process.env.SESSION_MAX_AGE!, 10) || 604800000;

      const cookieOptions: CookieOptions = {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        signed: true,
        sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
        path: '/',
        maxAge: sessionMaxAge,
      };

      if (process.env.NODE_ENV === 'production') {
        Object.assign(cookieOptions, { domain, expires: new Date(Date.now() + sessionMaxAge) });
      }

      res.cookie('staff_session', sessionToken, cookieOptions);
    }

    return sessionToken;
  } catch (error) {
    console.error('Error creating staff session cookie:', error);
    throw error;
  }
};
