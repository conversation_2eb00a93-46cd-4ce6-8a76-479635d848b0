import { createOptionsKeyMap } from '../utils/form.utils';

export const deliveryModeMap = {
  student_house: { key: 'student_house', label: "Student's House" },
  tutor_house: { key: 'tutor_house', label: "Tutor's House" },
  institute: { key: 'institute', label: 'Institute' },
  online: { key: 'online', label: 'Online' },
} as const;

export const deliveryModeOptions = createOptionsKeyMap(deliveryModeMap);
export type IDeliveryModeMap = keyof typeof deliveryModeMap;

export const startTimeMap = {
  immediately: { key: 'immediately', label: 'Immediately' },
  within_week: { key: 'within_week', label: 'Within a Week' },
  within_month: { key: 'within_month', label: 'Within a Month' },
  not_sure: { key: 'not_sure', label: 'Not Sure (Just Looking)' },
} as const;

export const startTimeOptions = createOptionsKeyMap(startTimeMap);
export type IStartTimeMap = keyof typeof startTimeMap;

export const tutorGenderMap = {
  male: { key: 'male', label: 'Male' },
  female: { key: 'female', label: 'Female' },
  any: { key: 'any', label: 'Any' },
} as const;

export const tutorGenderOptions = createOptionsKeyMap(tutorGenderMap);
export type ITutorGenderMap = keyof typeof tutorGenderMap;

export const enquiryStatusMap = {
  pending: { key: 'pending', label: 'Pending' },
  assigned: { key: 'assigned', label: 'Assigned' },
  completed: { key: 'completed', label: 'Completed' },
  cancelled: { key: 'cancelled', label: 'Cancelled' },
} as const;

export const enquiryStatusOptions = createOptionsKeyMap(enquiryStatusMap);
export type IEnquiryStatusMap = keyof typeof enquiryStatusMap;
