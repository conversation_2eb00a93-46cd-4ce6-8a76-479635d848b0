import mongoose, { Document, Schema } from 'mongoose';
import { CreateStreamInput } from '@/validation/schemas/education/college.schema';

export interface IStream extends CreateStreamInput {}

export interface StreamDocument extends IStream, Document {
  createdAt: Date;
  updatedAt: Date;
}

const streamSchema = new Schema<StreamDocument>(
  {
    name: {
      type: String,
      required: [true, 'Stream name is required'],
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
streamSchema.index({ name: 1 });

const Stream = mongoose.models.Stream || mongoose.model<StreamDocument>('Stream', streamSchema);

export default Stream;
