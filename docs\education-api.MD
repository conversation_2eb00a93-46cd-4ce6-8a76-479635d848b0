# Education API Documentation

This document outlines the routes, schemas, and controllers for the education-related components of the API.

## Overview

The education API provides endpoints for managing educational categories, boards, classes, subjects, and other educational entities. It follows a hierarchical structure where:

1. Service Categories (Schools, Colleges, Languages, etc.) are at the top level
2. Each category has its own hierarchy:
   - Schools: Board → Class → Subject
   - Colleges: Stream → Degree Level → Degree → Branch → Subject
   - Hobbies: Hobby Type → Hobby
   - Languages: Language Type → Language
   - IT Courses: Course Type → Course
   - Exams: Exam Category → Exam → Subject

## Base URL

All API endpoints are prefixed with `/api/v1/education`.

## Authentication

- Public endpoints can be accessed without authentication
- Admin-only endpoints require staff authentication with appropriate roles
- Most creation, update, and deletion operations require admin privileges

## Endpoints

### Service Categories

| Method | Endpoint                  | Description                     | Auth Required     |
| ------ | ------------------------- | ------------------------------- | ----------------- |
| GET    | `/service-categories`     | Get all service categories      | No                |
| GET    | `/service-categories/:id` | Get a specific service category | No                |
| POST   | `/service-categories`     | Create a new service category   | Yes (admin)       |
| PATCH  | `/service-categories/:id` | Update a service category       | Yes (admin)       |
| DELETE | `/service-categories/:id` | Delete a service category       | Yes (super_admin) |

### School Boards

| Method | Endpoint      | Description          | Auth Required     |
| ------ | ------------- | -------------------- | ----------------- |
| GET    | `/boards`     | Get all boards       | No                |
| GET    | `/boards/:id` | Get a specific board | No                |
| POST   | `/boards`     | Create a new board   | Yes (admin)       |
| PATCH  | `/boards/:id` | Update a board       | Yes (admin)       |
| DELETE | `/boards/:id` | Delete a board       | Yes (super_admin) |

### School Classes

| Method | Endpoint                  | Description          | Auth Required     |
| ------ | ------------------------- | -------------------- | ----------------- |
| GET    | `/classes`                | Get all classes      | No                |
| GET    | `/classes/board/:boardId` | Get classes by board | No                |
| GET    | `/classes/:id`            | Get a specific class | No                |
| POST   | `/classes`                | Create a new class   | Yes (admin)       |
| PATCH  | `/classes/:id`            | Update a class       | Yes (admin)       |
| DELETE | `/classes/:id`            | Delete a class       | Yes (super_admin) |

### School Subjects

| Method | Endpoint                   | Description            | Auth Required     |
| ------ | -------------------------- | ---------------------- | ----------------- |
| GET    | `/subjects`                | Get all subjects       | No                |
| GET    | `/subjects/class/:classId` | Get subjects by class  | No                |
| GET    | `/subjects/:id`            | Get a specific subject | No                |
| POST   | `/subjects`                | Create a new subject   | Yes (admin)       |
| PATCH  | `/subjects/:id`            | Update a subject       | Yes (admin)       |
| DELETE | `/subjects/:id`            | Delete a subject       | Yes (super_admin) |

### Hobby Types

| Method | Endpoint           | Description               | Auth Required     |
| ------ | ------------------ | ------------------------- | ----------------- |
| GET    | `/hobby-types`     | Get all hobby types       | No                |
| GET    | `/hobby-types/:id` | Get a specific hobby type | No                |
| POST   | `/hobby-types`     | Create a new hobby type   | Yes (admin)       |
| PATCH  | `/hobby-types/:id` | Update a hobby type       | Yes (admin)       |
| DELETE | `/hobby-types/:id` | Delete a hobby type       | Yes (super_admin) |

### Hobbies

| Method | Endpoint                     | Description          | Auth Required     |
| ------ | ---------------------------- | -------------------- | ----------------- |
| GET    | `/hobbies`                   | Get all hobbies      | No                |
| GET    | `/hobbies/type/:hobbyTypeId` | Get hobbies by type  | No                |
| GET    | `/hobbies/:id`               | Get a specific hobby | No                |
| POST   | `/hobbies`                   | Create a new hobby   | Yes (admin)       |
| PATCH  | `/hobbies/:id`               | Update a hobby       | Yes (admin)       |
| DELETE | `/hobbies/:id`               | Delete a hobby       | Yes (super_admin) |

### Language Types

| Method | Endpoint              | Description                  | Auth Required     |
| ------ | --------------------- | ---------------------------- | ----------------- |
| GET    | `/language-types`     | Get all language types       | No                |
| GET    | `/language-types/:id` | Get a specific language type | No                |
| POST   | `/language-types`     | Create a new language type   | Yes (admin)       |
| PATCH  | `/language-types/:id` | Update a language type       | Yes (admin)       |
| DELETE | `/language-types/:id` | Delete a language type       | Yes (super_admin) |

### Languages

| Method | Endpoint                          | Description             | Auth Required     |
| ------ | --------------------------------- | ----------------------- | ----------------- |
| GET    | `/languages`                      | Get all languages       | No                |
| GET    | `/languages/type/:languageTypeId` | Get languages by type   | No                |
| GET    | `/languages/:id`                  | Get a specific language | No                |
| POST   | `/languages`                      | Create a new language   | Yes (admin)       |
| PATCH  | `/languages/:id`                  | Update a language       | Yes (admin)       |
| DELETE | `/languages/:id`                  | Delete a language       | Yes (super_admin) |

### Course Types

| Method | Endpoint            | Description                | Auth Required     |
| ------ | ------------------- | -------------------------- | ----------------- |
| GET    | `/course-types`     | Get all course types       | No                |
| GET    | `/course-types/:id` | Get a specific course type | No                |
| POST   | `/course-types`     | Create a new course type   | Yes (admin)       |
| PATCH  | `/course-types/:id` | Update a course type       | Yes (admin)       |
| DELETE | `/course-types/:id` | Delete a course type       | Yes (super_admin) |

### Courses

| Method | Endpoint                      | Description           | Auth Required     |
| ------ | ----------------------------- | --------------------- | ----------------- |
| GET    | `/courses`                    | Get all courses       | No                |
| GET    | `/courses/type/:courseTypeId` | Get courses by type   | No                |
| GET    | `/courses/:id`                | Get a specific course | No                |
| POST   | `/courses`                    | Create a new course   | Yes (admin)       |
| PATCH  | `/courses/:id`                | Update a course       | Yes (admin)       |
| DELETE | `/courses/:id`                | Delete a course       | Yes (super_admin) |

### Exam Categories

| Method | Endpoint               | Description                  | Auth Required     |
| ------ | ---------------------- | ---------------------------- | ----------------- |
| GET    | `/exam-categories`     | Get all exam categories      | No                |
| GET    | `/exam-categories/:id` | Get a specific exam category | No                |
| POST   | `/exam-categories`     | Create a new exam category   | Yes (admin)       |
| PATCH  | `/exam-categories/:id` | Update an exam category      | Yes (admin)       |
| DELETE | `/exam-categories/:id` | Delete an exam category      | Yes (super_admin) |

### Exams

| Method | Endpoint                          | Description           | Auth Required     |
| ------ | --------------------------------- | --------------------- | ----------------- |
| GET    | `/exams`                          | Get all exams         | No                |
| GET    | `/exams/category/:examCategoryId` | Get exams by category | No                |
| GET    | `/exams/:id`                      | Get a specific exam   | No                |
| POST   | `/exams`                          | Create a new exam     | Yes (admin)       |
| PATCH  | `/exams/:id`                      | Update an exam        | Yes (admin)       |
| DELETE | `/exams/:id`                      | Delete an exam        | Yes (super_admin) |

### Exam Subjects

| Method | Endpoint                      | Description                 | Auth Required     |
| ------ | ----------------------------- | --------------------------- | ----------------- |
| GET    | `/exam-subjects`              | Get all exam subjects       | No                |
| GET    | `/exam-subjects/exam/:examId` | Get exam subjects by exam   | No                |
| GET    | `/exam-subjects/:id`          | Get a specific exam subject | No                |
| POST   | `/exam-subjects`              | Create a new exam subject   | Yes (admin)       |
| PATCH  | `/exam-subjects/:id`          | Update an exam subject      | Yes (admin)       |
| DELETE | `/exam-subjects/:id`          | Delete an exam subject      | Yes (super_admin) |

## Data Models

### Service Category

```typescript
{
  name: string; // enum: 'schools', 'colleges', 'languages', 'hobbies', 'it_courses', 'competitive_exams', 'entrance_exams'
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Board

```typescript
{
  name: string; // enum: 'cbse', 'icse', 'ib', 'state', 'dev'
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Class

```typescript
{
  name: string;
  displayOrder: number;
  board: ObjectId; // Reference to Board
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Subject

```typescript
{
  name: string;
  board: ObjectId; // Reference to Board
  class: ObjectId; // Reference to Class
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Hobby Type

```typescript
{
  name: string;
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Hobby

```typescript
{
  name: string;
  hobbyType: ObjectId; // Reference to HobbyType
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Language Type

```typescript
{
  name: string;
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Language

```typescript
{
  name: string;
  languageType: ObjectId; // Reference to LanguageType
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Course Type

```typescript
{
  name: string;
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Course

```typescript
{
  name: string;
  courseType: ObjectId; // Reference to CourseType
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Exam Category

```typescript
{
  name: string;
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Exam

```typescript
{
  name: string;
  examCategory: ObjectId; // Reference to ExamCategory
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

### Exam Subject

```typescript
{
  name: string;
  exam: ObjectId; // Reference to Exam
  isActive: boolean;
  createdBy?: ObjectId;
  updatedBy?: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}
```

## Seeding Data

To seed the initial education data, run:

```bash
npm run seed:education
```

This will populate the database with:

- Service categories
- School boards
- School classes
- College streams
- Degree levels
- Hobby types
- Language types
- Course types
- Exam categories

## Error Handling

The API returns standard HTTP status codes:

- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error

Error responses follow this format:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Error type"
}
```

## Pagination

List endpoints support pagination with these query parameters:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sort`: Field to sort by (default: createdAt)
- `order`: Sort order (asc or desc, default: desc)

Paginated responses include metadata:

```json
{
  "success": true,
  "message": "Items fetched successfully",
  "count": 5,
  "data": {
    "items": [...],
    "pagination": {
      "totalItems": 25,
      "itemsPerPage": 10,
      "currentPage": 1,
      "totalPages": 3,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```
