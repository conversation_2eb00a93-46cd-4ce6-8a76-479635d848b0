import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Subject from '@/models/school/subject.model';
import Board from '@/models/school/board.model';
import Class from '@/models/school/class.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createSubjectSchema, UpdateSubjectInput } from '@/validation/schemas/education/school.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const subjectAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'boards',
      localField: 'board',
      foreignField: '_id',
      as: 'boardDetails',
    },
  },
  {
    $lookup: {
      from: 'classes',
      localField: 'class',
      foreignField: '_id',
      as: 'classDetails',
    },
  },
  { $unwind: { path: '$boardDetails', preserveNullAndEmptyArrays: true } },
  { $unwind: { path: '$classDetails', preserveNullAndEmptyArrays: true } },
  { $sort: { name: 1 } },
];

export const createSubject = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createSubjectSchema, req.body);
  const { name, board, class: classId } = validatedData;

  if (!isValidObjectId(board)) {
    throw new BadRequestError('Please provide a valid board ID');
  }

  if (!isValidObjectId(classId)) {
    throw new BadRequestError('Please provide a valid class ID');
  }

  const boardExists = await Board.findById(board);
  if (!boardExists) {
    throw new NotFoundError(`No board found with id: ${board}`);
  }

  const classExists = await Class.findOne({ _id: classId, board });
  if (!classExists) {
    throw new NotFoundError(`No class found with id: ${classId} for the specified board`);
  }

  const existingSubject = await Subject.findOne({ name, board, class: classId });
  if (existingSubject) {
    throw new BadRequestError(`A subject with the name '${name}' already exists for this board and class`);
  }

  const subject = await Subject.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Subject created successfully',
    data: { subject },
  });
};

export const getAllSubjects = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.board && isValidObjectId(queryString.board)) {
    matchStage['board'] = new mongoose.Types.ObjectId(queryString.board as string);
    delete queryString.board;
  }

  if (queryString.class && isValidObjectId(queryString.class)) {
    matchStage['class'] = new mongoose.Types.ObjectId(queryString.class as string);
    delete queryString.class;
  }

  const baseAggregation = [{ $match: matchStage }, ...subjectAggregation];

  const queryManager = new AggregateQueryManager({
    model: Subject,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [subjects, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Subjects fetched successfully',
    data: { subjects, pagination },
  });
};

export const getSubjectsByClass = async (req: Request, res: Response): Promise<void> => {
  const { classId } = req.params;

  if (!isValidObjectId(classId)) {
    throw new BadRequestError('Please provide a valid class ID');
  }

  const classExists = await Class.findById(classId);
  if (!classExists) {
    throw new NotFoundError(`No class found with id: ${classId}`);
  }

  const subjects = await Subject.find({ class: classId, isActive: true }).sort({ name: 1 });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Subjects fetched successfully',
    data: { subjects },
  });
};

export const getSubjectById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid subject ID');
  }

  const [subject] = await Subject.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...subjectAggregation]);

  if (!subject) {
    throw new NotFoundError(`No subject found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Subject fetched successfully',
    data: { subject },
  });
};

export const updateSubject = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  const payload: UpdateSubjectInput = {};

  if (req.body.name) {
    payload['name'] = req.body.name;
  }

  if (req.body.isActive !== undefined) {
    payload['isActive'] = req.body.isActive;
  }

  const validatedData = await validateData(createSubjectSchema.partial(), payload);

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid subject ID');
  }

  const subject = await Subject.findById(id);
  if (!subject) {
    throw new NotFoundError(`No subject found with id: ${id}`);
  }

  if (validatedData.name) {
    const existingSubject = await Subject.findOne({
      name: validatedData.name,
      board: subject.board,
      class: subject.class,
      _id: { $ne: id },
    });

    if (existingSubject) {
      throw new BadRequestError(`A subject with the name '${validatedData.name}' already exists for this board and class`);
    }
  }

  const updatedSubject = await Subject.findByIdAndUpdate(id, validatedData, { new: true, runValidators: true });

  if (!updatedSubject) {
    throw new NotFoundError(`No subject found with id: ${id}`);
  }

  const [newSubject] = await Subject.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...subjectAggregation]);

  if (!newSubject) {
    throw new NotFoundError(`No subject found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Subject updated successfully',
    data: { subject: newSubject },
  });
};

export const deleteSubject = async (req: Request<{ id: string }>, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid subject ID');
  }

  const subject = await Subject.findByIdAndDelete(id);
  if (!subject) {
    throw new NotFoundError(`No subject found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Subject deleted successfully',
  });
};
