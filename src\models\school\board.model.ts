import { CreateBoardInput } from '@/validation/schemas/education/school.schema';
import mongoose, { Document, Schema } from 'mongoose';

export interface IBoard extends CreateBoardInput {}

export interface BoardDocument extends IBoard, Document {
  createdAt: Date;
  updatedAt: Date;
}

const boardSchema = new Schema<BoardDocument>(
  {
    name: {
      type: String,
      required: [true, 'Board name is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
boardSchema.index({ name: 1 });

const Board = mongoose.models.Board || mongoose.model<BoardDocument>('Board', boardSchema);

export default Board;
