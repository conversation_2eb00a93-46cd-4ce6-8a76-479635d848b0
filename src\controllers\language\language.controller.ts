import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Language from '@/models/language/language.model';
import LanguageType from '@/models/language/language-type.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createLanguageSchema, UpdateLanguageInput } from '@/validation/schemas/education/language.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const languageAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'languagetypes',
      localField: 'languageType',
      foreignField: '_id',
      as: 'languageTypeDetails',
    },
  },
  { $unwind: { path: '$languageTypeDetails', preserveNullAndEmptyArrays: true } },
  { $sort: { 'languageTypeDetails.name': 1, name: 1 } },
];

/**
 * Create a new language
 * @route POST /api/v1/education/languages
 * @access Admin only
 */
export const createLanguage = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createLanguageSchema, req.body);
  const { name, languageType } = validatedData;

  if (!isValidObjectId(languageType)) {
    throw new BadRequestError('Please provide a valid language type ID');
  }

  const languageTypeExists = await LanguageType.findById(languageType);
  if (!languageTypeExists) {
    throw new NotFoundError(`No language type found with id: ${languageType}`);
  }

  const existingLanguage = await Language.findOne({ name, languageType });
  if (existingLanguage) {
    throw new BadRequestError(`A language with the name '${name}' already exists for this language type`);
  }

  const language = await Language.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Language created successfully',
    data: { language },
  });
};

/**
 * Get all languages
 * @route GET /api/v1/education/languages
 * @access Public
 */
export const getAllLanguages = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.languageType && isValidObjectId(queryString.languageType)) {
    matchStage['languageType'] = new mongoose.Types.ObjectId(queryString.languageType as string);
    delete queryString.languageType;
  }

  const baseAggregation = [{ $match: matchStage }, ...languageAggregation];

  const queryManager = new AggregateQueryManager({
    model: Language,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [languages, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Languages fetched successfully',
    data: { languages, pagination },
  });
};

/**
 * Get languages by language type
 * @route GET /api/v1/education/languages/type/:languageTypeId
 * @access Public
 */
export const getLanguagesByType = async (req: Request, res: Response): Promise<void> => {
  const { languageTypeId } = req.params;
  const queryString = req.query;

  if (!isValidObjectId(languageTypeId)) {
    throw new BadRequestError('Please provide a valid language type ID');
  }

  const languageTypeExists = await LanguageType.findById(languageTypeId);
  if (!languageTypeExists) {
    throw new NotFoundError(`No language type found with id: ${languageTypeId}`);
  }

  const matchStage = { languageType: new mongoose.Types.ObjectId(languageTypeId) };
  const baseAggregation = [{ $match: matchStage }, ...languageAggregation];

  const queryManager = new AggregateQueryManager({
    model: Language,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [languages, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Languages fetched successfully',
    data: { languages, pagination, languageType: languageTypeExists },
  });
};

/**
 * Get a specific language by ID
 * @route GET /api/v1/education/languages/:id
 * @access Public
 */
export const getLanguageById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid language ID');
  }

  const [language] = await Language.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...languageAggregation,
  ]);

  if (!language) {
    throw new NotFoundError(`No language found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Language fetched successfully',
    data: { language },
  });
};

/**
 * Update a language
 * @route PATCH /api/v1/education/languages/:id
 * @access Admin only
 */
export const updateLanguage = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateLanguageInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid language ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const language = await Language.findById(id);
  if (!language) {
    throw new NotFoundError(`No language found with id: ${id}`);
  }

  // Validate language type if provided
  if (updateData.languageType && !isValidObjectId(updateData.languageType)) {
    throw new BadRequestError('Please provide a valid language type ID');
  }

  if (updateData.languageType) {
    const languageTypeExists = await LanguageType.findById(updateData.languageType);
    if (!languageTypeExists) {
      throw new NotFoundError(`No language type found with id: ${updateData.languageType}`);
    }
  }

  // Validate the update data
  const validatedData = await validateData(createLanguageSchema.partial(), updateData);

  // Check for duplicate name within the same language type
  if (validatedData.name || validatedData.languageType) {
    const languageTypeId = validatedData.languageType || language.languageType;
    const languageName = validatedData.name || language.name;

    const existingLanguage = await Language.findOne({
      name: languageName,
      languageType: languageTypeId,
      _id: { $ne: id },
    });

    if (existingLanguage) {
      throw new BadRequestError(`A language with the name '${languageName}' already exists for this language type`);
    }
  }

  await Language.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated language with aggregation
  const [updatedLanguage] = await Language.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...languageAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Language updated successfully',
    data: { language: updatedLanguage },
  });
};

/**
 * Delete a language
 * @route DELETE /api/v1/education/languages/:id
 * @access Super Admin only
 */
export const deleteLanguage = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid language ID');
  }

  const language = await Language.findById(id);
  if (!language) {
    throw new NotFoundError(`No language found with id: ${id}`);
  }

  // TODO: Check if there are any references to this language in other collections
  // If yes, prevent deletion or implement cascade delete based on requirements

  await Language.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Language deleted successfully',
    data: null,
  });
};
