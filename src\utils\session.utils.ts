import { Request } from 'express';
import Session from '@/models/session.model';
import StaffSession from '@/models/staff-session.model';
import { generateSessionId } from './crypto.utils';
import mongoose from 'mongoose';

const MAX_SESSIONS_PER_USER = 1;

export const updateOrCreateUserSession = async (
  userId: string | mongoose.Types.ObjectId,
  req: Request
): Promise<{ sessionId: string; expiresAt: Date }> => {
  try {
    if (!userId) {
      throw new Error('User ID is required for session creation');
    }

    const sessionId = generateSessionId();
    const sessionLifetime = Number.parseInt(process.env.SESSION_MAX_AGE!, 10) || 604800000;
    const expiresAt = new Date(Date.now() + sessionLifetime);

    const existingSession = await Session.findOne({ user: userId, isValid: true });

    if (existingSession) {
      existingSession.sessionId = sessionId;
      existingSession.IP = req.ip;
      existingSession.userAgent = req.headers['user-agent'] as string;
      existingSession.expiresAt = expiresAt;
      await existingSession.save();
    } else {
      const activeSessions = await Session.find({ user: userId, isValid: true })
        .sort({ updatedAt: -1 })
        .skip(MAX_SESSIONS_PER_USER - 1);

      if (activeSessions.length > 0) {
        const sessionIds = activeSessions.map((session) => session._id);
        await Session.updateMany({ _id: { $in: sessionIds } }, { isValid: false });
      }

      await Session.create({
        sessionId,
        IP: req.ip,
        userAgent: req.headers['user-agent'],
        user: userId,
        userType: 'user',
        expiresAt,
        isValid: true,
      });
    }

    return { sessionId, expiresAt };
  } catch (error) {
    console.error('Error creating/updating user session:', error);
    throw new Error(`Failed to create/update user session: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

export const updateOrCreateStaffSession = async (
  staffId: string | mongoose.Types.ObjectId,
  req: Request
): Promise<{ sessionId: string; expiresAt: Date }> => {
  try {
    if (!staffId) {
      throw new Error('Staff ID is required for session creation');
    }

    const sessionId = generateSessionId();
    const sessionLifetime = Number.parseInt(process.env.SESSION_MAX_AGE!, 10) || 604800000;
    const expiresAt = new Date(Date.now() + sessionLifetime);

    const existingSession = await StaffSession.findOne({ staff: staffId, isValid: true });

    if (existingSession) {
      existingSession.sessionId = sessionId;
      existingSession.IP = req.ip;
      existingSession.userAgent = req.headers['user-agent'] as string;
      existingSession.expiresAt = expiresAt;
      await existingSession.save();
    } else {
      const activeSessions = await StaffSession.find({ staff: staffId, isValid: true })
        .sort({ updatedAt: -1 })
        .skip(MAX_SESSIONS_PER_USER - 1);

      if (activeSessions.length > 0) {
        const sessionIds = activeSessions.map((session) => session._id);
        await StaffSession.updateMany({ _id: { $in: sessionIds } }, { isValid: false });
      }

      await StaffSession.create({
        sessionId,
        IP: req.ip,
        userAgent: req.headers['user-agent'],
        staff: staffId,
        expiresAt,
        isValid: true,
      });
    }

    return { sessionId, expiresAt };
  } catch (error) {
    console.error('Error creating/updating staff session:', error);
    throw new Error(`Failed to create/update staff session: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
