import express from 'express';
import {
  createDegreeLevel,
  getAllDegreeLevels,
  getDegreeLevelById,
  updateDegreeLevel,
  deleteDegreeLevel,
} from '@/controllers/college/degree-level.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllDegreeLevels);
router.get('/:id', getDegreeLevelById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createDegreeLevel);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateDegreeLevel);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteDegreeLevel);

export default router;
