import mongoose, { Document, Schema } from 'mongoose';
import { tuitionTypeMap } from '@/validation/schemas/tutor/tuition.maps';
import { CreateTeachingExperienceInput } from '@/validation/schemas/tutor/profiles/teaching-experience.schema';

export interface ITeachingExperience extends Omit<CreateTeachingExperienceInput, 'businessLocationId'> {
  userId: mongoose.Types.ObjectId;
  businessLocationId?: mongoose.Types.ObjectId;
}

export interface TeachingExperienceDocument extends ITeachingExperience, Document {
  createdAt: Date;
  updatedAt: Date;
}

const teachingExperienceSchema = new Schema<TeachingExperienceDocument>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    tuitionType: {
      type: String,
      enum: Object.keys(tuitionTypeMap),
      required: [true, 'Tuition type is required'],
    },
    experienceMonths: {
      type: Number,
      required: [true, 'Experience duration is required'],
      min: [0, 'Experience must be non-negative'],
    },
    placeName: {
      type: String,
      required: [true, 'Place name is required'],
      trim: true,
    },
    location: {
      type: String,
      required: [true, 'Location is required'],
      trim: true,
    },
    businessLocationId: {
      type: Schema.Types.ObjectId,
      ref: 'BusinessLocation',
      required: function (this: ITeachingExperience) {
        return this.tuitionType !== 'private';
      },
    },
  },
  { timestamps: true }
);

// Add indexes to optimize queries
teachingExperienceSchema.index({ userId: 1 });
teachingExperienceSchema.index({ tuitionType: 1 });
teachingExperienceSchema.index({ isActive: 1 });
teachingExperienceSchema.index({ businessLocationId: 1 });

const TeachingExperience =
  mongoose.models.TeachingExperience || mongoose.model<TeachingExperienceDocument>('TeachingExperience', teachingExperienceSchema);

export default TeachingExperience;
