import express from 'express';
import { createHobby, getAllHobbies, getHobbyById, updateHobby, deleteHobby } from '@/controllers/hobby/hobby.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllHobbies);
router.get('/:id', getHobbyById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createHobby);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateHobby);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteHobby);

export default router;
