import mongoose, { Document, Schema } from 'mongoose';
import { CreateLanguageInput } from '@/validation/schemas/education/language.schema';

export interface ILanguage extends Omit<CreateLanguageInput, 'languageType'> {
  languageType: mongoose.Types.ObjectId;
}

export interface LanguageDocument extends ILanguage, Document {
  createdAt: Date;
  updatedAt: Date;
}

const languageSchema = new Schema<LanguageDocument>(
  {
    name: {
      type: String,
      required: [true, 'Language name is required'],
      trim: true,
    },
    languageType: {
      type: Schema.Types.ObjectId,
      ref: 'LanguageType',
      required: [true, 'Language type is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
languageSchema.index({ languageType: 1, name: 1 }, { unique: true });

const Language = mongoose.models.Language || mongoose.model<LanguageDocument>('Language', languageSchema);

export default Language;
