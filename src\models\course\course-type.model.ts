import mongoose, { Document, Schema } from 'mongoose';
import { CreateCourseTypeInput } from '@/validation/schemas/education/course.schema';

export interface ICourseType extends CreateCourseTypeInput {}

export interface CourseTypeDocument extends ICourseType, Document {
  createdAt: Date;
  updatedAt: Date;
}

const courseTypeSchema = new Schema<CourseTypeDocument>(
  {
    name: {
      type: String,
      required: [true, 'Course type name is required'],
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
courseTypeSchema.index({ name: 1 });

const CourseType = mongoose.models.CourseType || mongoose.model<CourseTypeDocument>('CourseType', courseTypeSchema);

export default CourseType;
