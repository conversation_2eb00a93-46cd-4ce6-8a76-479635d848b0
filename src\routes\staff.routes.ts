import express from 'express';
import { createStaff, deleteStaff, getAllStaff, getStaffById, updateStaff, getCurrentStaff } from '@/controllers/staff.controller';
import { staffLogin, staffLogout, updateStaffPassword, refreshStaffToken } from '@/controllers/staff-auth.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';
import { authLimiter } from '@/middleware/rate-limit.middleware';

const router = express.Router();

// Auth routes
router.post('/login', authLimiter, staffLogin);
router.delete('/logout', authenticatedStaff, staffLogout);
router.post('/refresh-token', authenticatedStaff, refreshStaffToken);

// Staff member (self) routes
router.patch('/update-password', authenticatedStaff, updateStaffPassword);
router.get('/me', authenticatedStaff, getCurrentStaff);

// Admin-only routes
router
  .route('/')
  .post([authenticatedStaff, authorizeStaffRoles('super_admin')], createStaff)
  .get([authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], getAllStaff);

router
  .route('/:id')
  .get([authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], getStaffById)
  .patch([authenticatedStaff, authorizeStaffRoles('super_admin')], updateStaff)
  .delete([authenticatedStaff, authorizeStaffRoles('super_admin')], deleteStaff);

export default router;
