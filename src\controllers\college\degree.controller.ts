import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Degree from '@/models/college/degree.model';
import DegreeLevel from '@/models/college/degree-level.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createDegreeSchema, UpdateDegreeInput } from '@/validation/schemas/education/college.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const degreeAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'degreelevels',
      localField: 'degreeLevel',
      foreignField: '_id',
      as: 'degreeLevelDetails',
    },
  },
  {
    $lookup: {
      from: 'streams',
      localField: 'degreeLevelDetails.stream',
      foreignField: '_id',
      as: 'streamDetails',
    },
  },
  {
    $addFields: {
      degreeLevelDetails: { $arrayElemAt: ['$degreeLevelDetails', 0] },
      streamDetails: { $arrayElemAt: ['$streamDetails', 0] },
    },
  },
  { $sort: { name: 1 } },
];

export const createDegree = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createDegreeSchema, req.body);
  const { name, degreeLevel } = validatedData;

  if (!isValidObjectId(degreeLevel)) {
    throw new BadRequestError('Please provide a valid degree level ID');
  }

  const degreeLevelExists = await DegreeLevel.findById(degreeLevel);
  if (!degreeLevelExists) {
    throw new NotFoundError(`No degree level found with id: ${degreeLevel}`);
  }

  const existingDegree = await Degree.findOne({ name, degreeLevel });
  if (existingDegree) {
    throw new BadRequestError(`A degree with the name '${name}' already exists for this degree level`);
  }

  const degree = await Degree.create({
    ...validatedData,
  });

  const [populatedDegree] = await Degree.aggregate([
    { $match: { _id: degree._id } },
    ...degreeAggregation,
  ]);

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Degree created successfully',
    data: { degree: populatedDegree },
  });
};

export const getAllDegrees = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.degreeLevel && isValidObjectId(queryString.degreeLevel)) {
    matchStage['degreeLevel'] = new mongoose.Types.ObjectId(queryString.degreeLevel as string);
    delete queryString.degreeLevel;
  }

  const baseAggregation = [{ $match: matchStage }, ...degreeAggregation];

  const queryManager = new AggregateQueryManager({
    model: Degree,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [degrees, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Degrees fetched successfully',
    data: { degrees, pagination },
  });
};

export const getDegreeById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid degree ID');
  }

  const [degree] = await Degree.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...degreeAggregation,
  ]);

  if (!degree) {
    throw new NotFoundError(`No degree found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Degree fetched successfully',
    data: { degree },
  });
};

export const updateDegree = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateDegreeInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid degree ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const degree = await Degree.findById(id);
  if (!degree) {
    throw new NotFoundError(`No degree found with id: ${id}`);
  }

  // Validate degree level if provided
  if (updateData.degreeLevel && !isValidObjectId(updateData.degreeLevel)) {
    throw new BadRequestError('Please provide a valid degree level ID');
  }

  if (updateData.degreeLevel) {
    const degreeLevelExists = await DegreeLevel.findById(updateData.degreeLevel);
    if (!degreeLevelExists) {
      throw new NotFoundError(`No degree level found with id: ${updateData.degreeLevel}`);
    }
  }

  // Check for duplicate if name or degree level is being updated
  if (updateData.name || updateData.degreeLevel) {
    const name = updateData.name || degree.name;
    const degreeLevel = updateData.degreeLevel || degree.degreeLevel;

    const existingDegree = await Degree.findOne({
      name,
      degreeLevel,
      _id: { $ne: id },
    });

    if (existingDegree) {
      throw new BadRequestError(`A degree with the name '${name}' already exists for this degree level`);
    }
  }

  await Degree.findByIdAndUpdate(id, updateData, { runValidators: true });

  const [updatedDegree] = await Degree.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...degreeAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Degree updated successfully',
    data: { degree: updatedDegree },
  });
};

export const deleteDegree = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid degree ID');
  }

  const degree = await Degree.findById(id);
  if (!degree) {
    throw new NotFoundError(`No degree found with id: ${id}`);
  }

  // TODO: Check if there are any branches associated with this degree
  // If yes, prevent deletion or implement cascade delete based on requirements

  await Degree.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Degree deleted successfully',
    data: null,
  });
};
