import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Course from '@/models/course/course.model';
import CourseType from '@/models/course/course-type.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createCourseSchema, UpdateCourseInput } from '@/validation/schemas/education/course.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const courseAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'coursetypes',
      localField: 'courseType',
      foreignField: '_id',
      as: 'courseTypeDetails',
    },
  },
  { $unwind: { path: '$courseTypeDetails', preserveNullAndEmptyArrays: true } },
  { $sort: { 'courseTypeDetails.name': 1, name: 1 } },
];

/**
 * Create a new course
 * @route POST /api/v1/education/courses
 * @access Admin only
 */
export const createCourse = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createCourseSchema, req.body);
  const { name, courseType } = validatedData;

  if (!isValidObjectId(courseType)) {
    throw new BadRequestError('Please provide a valid course type ID');
  }

  const courseTypeExists = await CourseType.findById(courseType);
  if (!courseTypeExists) {
    throw new NotFoundError(`No course type found with id: ${courseType}`);
  }

  const existingCourse = await Course.findOne({ name, courseType });
  if (existingCourse) {
    throw new BadRequestError(`A course with the name '${name}' already exists for this course type`);
  }

  const course = await Course.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Course created successfully',
    data: { course },
  });
};

/**
 * Get all courses
 * @route GET /api/v1/education/courses
 * @access Public
 */
export const getAllCourses = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.courseType && isValidObjectId(queryString.courseType)) {
    matchStage['courseType'] = new mongoose.Types.ObjectId(queryString.courseType as string);
    delete queryString.courseType;
  }

  const baseAggregation = [{ $match: matchStage }, ...courseAggregation];

  const queryManager = new AggregateQueryManager({
    model: Course,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [courses, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Courses fetched successfully',
    data: { courses, pagination },
  });
};

/**
 * Get courses by course type
 * @route GET /api/v1/education/courses/type/:courseTypeId
 * @access Public
 */
export const getCoursesByType = async (req: Request, res: Response): Promise<void> => {
  const { courseTypeId } = req.params;
  const queryString = req.query;

  if (!isValidObjectId(courseTypeId)) {
    throw new BadRequestError('Please provide a valid course type ID');
  }

  const courseTypeExists = await CourseType.findById(courseTypeId);
  if (!courseTypeExists) {
    throw new NotFoundError(`No course type found with id: ${courseTypeId}`);
  }

  const matchStage = { courseType: new mongoose.Types.ObjectId(courseTypeId) };
  const baseAggregation = [{ $match: matchStage }, ...courseAggregation];

  const queryManager = new AggregateQueryManager({
    model: Course,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [courses, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Courses fetched successfully',
    data: { courses, pagination, courseType: courseTypeExists },
  });
};

/**
 * Get a specific course by ID
 * @route GET /api/v1/education/courses/:id
 * @access Public
 */
export const getCourseById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid course ID');
  }

  const [course] = await Course.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...courseAggregation]);

  if (!course) {
    throw new NotFoundError(`No course found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Course fetched successfully',
    data: { course },
  });
};

/**
 * Update a course
 * @route PATCH /api/v1/education/courses/:id
 * @access Admin only
 */
export const updateCourse = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateCourseInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid course ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const course = await Course.findById(id);
  if (!course) {
    throw new NotFoundError(`No course found with id: ${id}`);
  }

  // Validate course type if provided
  if (updateData.courseType && !isValidObjectId(updateData.courseType)) {
    throw new BadRequestError('Please provide a valid course type ID');
  }

  if (updateData.courseType) {
    const courseTypeExists = await CourseType.findById(updateData.courseType);
    if (!courseTypeExists) {
      throw new NotFoundError(`No course type found with id: ${updateData.courseType}`);
    }
  }

  // Validate the update data
  const validatedData = await validateData(createCourseSchema.partial(), updateData);

  // Check for duplicate name within the same course type
  if (validatedData.name || validatedData.courseType) {
    const courseTypeId = validatedData.courseType || course.courseType;
    const courseName = validatedData.name || course.name;

    const existingCourse = await Course.findOne({
      name: courseName,
      courseType: courseTypeId,
      _id: { $ne: id },
    });

    if (existingCourse) {
      throw new BadRequestError(`A course with the name '${courseName}' already exists for this course type`);
    }
  }

  await Course.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated course with aggregation
  const [updatedCourse] = await Course.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...courseAggregation]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Course updated successfully',
    data: { course: updatedCourse },
  });
};

/**
 * Delete a course
 * @route DELETE /api/v1/education/courses/:id
 * @access Super Admin only
 */
export const deleteCourse = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid course ID');
  }

  const course = await Course.findById(id);
  if (!course) {
    throw new NotFoundError(`No course found with id: ${id}`);
  }

  // TODO: Check if there are any references to this course in other collections
  // If yes, prevent deletion or implement cascade delete based on requirements

  await Course.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Course deleted successfully',
    data: null,
  });
};
