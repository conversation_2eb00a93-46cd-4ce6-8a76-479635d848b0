import express from 'express';
import {
  createSubject,
  getAllSubjects,
  getSubjectsByClass,
  getSubjectById,
  updateSubject,
  deleteSubject,
} from '@/controllers/school/subject.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllSubjects);
router.get('/class/:classId', getSubjectsByClass);
router.get('/:id', getSubjectById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createSubject);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateSubject);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteSubject);

export default router;
