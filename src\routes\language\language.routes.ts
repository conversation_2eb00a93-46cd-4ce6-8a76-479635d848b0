import express from 'express';
import {
  createLanguage,
  getAllLanguages,
  getLanguagesByType,
  getLanguageById,
  updateLanguage,
  deleteLanguage,
} from '@/controllers/language/language.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllLanguages);
router.get('/type/:languageTypeId', getLanguagesByType);
router.get('/:id', getLanguageById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createLanguage);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateLanguage);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteLanguage);

export default router;
