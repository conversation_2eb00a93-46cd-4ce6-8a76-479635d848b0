import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Class from '@/models/school/class.model';
import Board from '@/models/school/board.model';
import Subject from '@/models/school/subject.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createClassSchema, UpdateClassInput } from '@/validation/schemas/education/school.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const classAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'boards',
      localField: 'board',
      foreignField: '_id',
      as: 'boardDetails',
    },
  },
  { $unwind: { path: '$boardDetails', preserveNullAndEmptyArrays: true } },
  { $sort: { displayOrder: 1 } },
];

export const createClass = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createClassSchema, req.body);
  const { name, board } = validatedData;

  if (!isValidObjectId(board)) {
    throw new BadRequestError('Please provide a valid board ID');
  }

  const boardExists = await Board.findById(board);
  if (!boardExists) {
    throw new NotFoundError(`No board found with id: ${board}`);
  }

  const existingClass = await Class.findOne({ name, board });
  if (existingClass) {
    throw new BadRequestError(`A class with the name '${name}' already exists for this board`);
  }

  const classObj = await Class.create(validatedData);

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Class created successfully',
    data: { class: classObj },
  });
};

export const getAllClasses = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.board && isValidObjectId(queryString.board)) {
    matchStage['board'] = new mongoose.Types.ObjectId(queryString.board as string);
    delete queryString.board;
  }

  const baseAggregation = [{ $match: matchStage }, ...classAggregation];

  const queryManager = new AggregateQueryManager({
    model: Class,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [classes, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Classes fetched successfully',
    data: { classes, pagination },
  });
};

export const getClassesByBoard = async (req: Request, res: Response): Promise<void> => {
  const { boardId } = req.params;

  if (!isValidObjectId(boardId)) {
    throw new BadRequestError('Please provide a valid board ID');
  }

  const boardExists = await Board.findById(boardId);
  if (!boardExists) {
    throw new NotFoundError(`No board found with id: ${boardId}`);
  }

  const classes = await Class.find({ board: boardId, isActive: true }).sort({ displayOrder: 1 });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Classes fetched successfully',
    count: classes.length,
    data: { classes },
  });
};

export const getClassById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid class ID');
  }

  const [classObj] = await Class.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...classAggregation]);

  if (!classObj) {
    throw new NotFoundError(`No class found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Class fetched successfully',
    data: { class: classObj },
  });
};

export const updateClass = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  const paylaoad: UpdateClassInput = {};

  if (req.body.name) {
    paylaoad['name'] = req.body.name;
  }

  if (req.body.displayOrder !== undefined) {
    paylaoad['displayOrder'] = req.body.displayOrder;
  }

  if (req.body.isActive !== undefined) {
    paylaoad['isActive'] = req.body.isActive;
  }

  const validatedData = await validateData(createClassSchema.partial(), paylaoad);

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid class ID');
  }

  const classObj = await Class.findById(id);

  if (!classObj) {
    throw new NotFoundError(`No class found with id: ${id}`);
  }

  if (validatedData.name) {
    const existingClass = await Class.findOne({
      name: validatedData.name,
      board: classObj.board,
      _id: { $ne: id },
    });

    if (existingClass) {
      throw new BadRequestError(`A class with the name '${validatedData.name}' already exists for this board`);
    }
  }

  const updatedClass = await Class.findByIdAndUpdate(id, validatedData, { new: true, runValidators: true });

  if (!updatedClass) {
    throw new NotFoundError(`No class found with id: ${id}`);
  }

  const [newClass] = await Class.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...classAggregation]);

  if (!newClass) {
    throw new NotFoundError(`No class found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Class updated successfully',
    data: { class: newClass },
  });
};

export const deleteClass = async (req: Request<{ id: string }>, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid class ID');
  }

  const subjectsUsingClass = await Subject.countDocuments({ class: id });

  if (subjectsUsingClass > 0) {
    throw new BadRequestError(`Cannot delete class as it is associated with ${subjectsUsingClass} subjects. Please delete those subjects first.`);
  }

  const classObj = await Class.findByIdAndDelete(id);

  if (!classObj) {
    throw new NotFoundError(`No class found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Class deleted successfully',
  });
};
