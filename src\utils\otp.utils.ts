// 1. Generate OTP
export const generateOTP = (length: number): string => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  const otp = Math.floor(min + Math.random() * (max - min + 1)).toString();
  return otp;
};

// 2. Send OTP
export const sendOTP = async (phone: string, otp: string): Promise<void> => {
  console.log(`Sending OTP ${otp} to ${phone}...`);
};

// 3. Format phone number
export const formatPhoneNumber = (phone: string): string => {
  const digits = phone.replace(/\D/g, '');
  return digits;
};
