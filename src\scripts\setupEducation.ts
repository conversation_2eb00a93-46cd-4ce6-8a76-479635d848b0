// Setup education data for the application
import fs from 'fs';
import path from 'path';
import ServiceCategory from '@/models/service-category.model';
import Board from '@/models/school/board.model';
import Class from '@/models/school/class.model';
import Subject from '@/models/school/subject.model';
import Stream from '@/models/college/stream.model';
import DegreeLevel from '@/models/college/degree-level.model';
import Degree from '@/models/college/degree.model';
import Branch from '@/models/college/branch.model';
import LanguageType from '@/models/language/language-type.model';
import Language from '@/models/language/language.model';
import HobbyType from '@/models/hobby/hobby-type.model';
import Hobby from '@/models/hobby/hobby.model';
import CourseType from '@/models/course/course-type.model';
import Course from '@/models/course/course.model';
import ExamCategory from '@/models/exam/exam-category.model';
import Exam from '@/models/exam/exam.model';
import ExamSubject from '@/models/exam/exam-subject.model';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import dbConnect from '@/config/database';
import dotenv from 'dotenv';

dotenv.config();

const mongoUri = process.env.MONGO_URI as string;

// Maps for different educational entities
const boardMap = {
  cbse: { key: 'cbse', label: 'CBSE' },
  icse: { key: 'icse', label: 'ICSE' },
  ib: { key: 'ib', label: 'IB' },
  state: { key: 'state', label: 'State Board' },
  dev: { key: 'dev', label: 'DEV' },
} as const;

const classMap = {
  nursery_kg: { key: 'Nursery-KG', displayOrder: 1 },
  class_1: { key: 'Class I', displayOrder: 2 },
  class_2: { key: 'Class II', displayOrder: 3 },
  class_3: { key: 'Class III', displayOrder: 4 },
  class_4: { key: 'Class IV', displayOrder: 5 },
  class_5: { key: 'Class V', displayOrder: 6 },
  class_6: { key: 'Class VI', displayOrder: 7 },
  class_7: { key: 'Class VII', displayOrder: 8 },
  class_8: { key: 'Class VIII', displayOrder: 9 },
  class_9: { key: 'Class IX', displayOrder: 10 },
  class_10: { key: 'Class X', displayOrder: 11 },
  class_11: { key: 'Class XI', displayOrder: 12 },
  class_12: { key: 'Class XII', displayOrder: 13 },
} as const;

const subjectMap = {
  mathematics: { key: 'Mathematics' },
  science: { key: 'Science' },
  english: { key: 'English' },
  hindi: { key: 'Hindi' },
  social_science: { key: 'Social Science' },
  physics: { key: 'Physics' },
  chemistry: { key: 'Chemistry' },
  biology: { key: 'Biology' },
  history: { key: 'History' },
  geography: { key: 'Geography' },
  economics: { key: 'Economics' },
  political_science: { key: 'Political Science' },
  computer_science: { key: 'Computer Science' },
  commerce: { key: 'Commerce' },
  accountancy: { key: 'Accountancy' },
  business_studies: { key: 'Business Studies' },
} as const;

const streamMap = {
  engineering: { key: 'Engineering' },
  medical: { key: 'Medical' },
  commerce: { key: 'Commerce' },
  arts: { key: 'Arts' },
  science: { key: 'Science' },
  management: { key: 'Management' },
  law: { key: 'Law' },
} as const;

const degreeLevelMap = {
  diploma: { key: 'Diploma' },
  graduation: { key: 'Graduation' },
  post_graduation: { key: 'Post Graduation' },
  doctorate: { key: 'Doctorate' },
} as const;

const languageTypeMap = {
  indian: { key: 'Indian Languages' },
  foreign: { key: 'Foreign Languages' },
} as const;

const hobbyTypeMap = {
  music: { key: 'Music' },
  dance: { key: 'Dance' },
  art: { key: 'Art & Craft' },
  sports: { key: 'Sports' },
  cooking: { key: 'Cooking' },
} as const;

const courseTypeMap = {
  programming: { key: 'Programming' },
  design: { key: 'Design' },
  data_science: { key: 'Data Science' },
  web_development: { key: 'Web Development' },
  mobile_development: { key: 'Mobile Development' },
} as const;

const examCategoryMap = {
  competitive: { key: 'Competitive Exams' },
  entrance: { key: 'Entrance Exams' },
} as const;

// Data collection for writing to file
const educationData: {
  serviceCategories: { name: string }[];
  boards: { name: string }[];
  classes: { name: string }[];
  subjects: { name: string }[];
  streams: { name: string }[];
  degreeLevels: { name: string }[];
  degrees: { name: string }[];
  branches: { name: string }[];
  languageTypes: { name: string }[];
  languages: { name: string }[];
  hobbyTypes: { name: string }[];
  hobbies: { name: string }[];
  courseTypes: { name: string }[];
  courses: { name: string }[];
  examCategories: { name: string }[];
  exams: { name: string }[];
  examSubjects: { name: string }[];
} = {
  serviceCategories: [],
  boards: [],
  classes: [],
  subjects: [],
  streams: [],
  degreeLevels: [],
  degrees: [],
  branches: [],
  languageTypes: [],
  languages: [],
  hobbyTypes: [],
  hobbies: [],
  courseTypes: [],
  courses: [],
  examCategories: [],
  exams: [],
  examSubjects: [],
};

// Function to write education data to file
async function writeEducationDataToFile() {
  try {
    const filePath = path.resolve(__dirname, '../../data/education-data.json');

    // Create directory if it doesn't exist
    const dirPath = path.dirname(filePath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    // Write data to file
    fs.writeFileSync(filePath, JSON.stringify(educationData, null, 2));
    console.log(`Education data written to ${filePath}`);

    // Also write as markdown for better readability
    const mdFilePath = path.resolve(__dirname, '../../data/education-data.md');

    let mdContent = '# Educational Data\n\n';

    // Service Categories
    mdContent += '## Service Categories\n\n';
    educationData.serviceCategories.forEach((category) => {
      mdContent += `- ${category.name}\n`;
    });
    mdContent += '\n';

    // Boards
    mdContent += '## Boards\n\n';
    educationData.boards.forEach((board) => {
      mdContent += `- ${board.name}\n`;
    });
    mdContent += '\n';

    // Classes
    mdContent += '## Classes\n\n';
    educationData.classes.forEach((cls) => {
      mdContent += `- ${cls.name}\n`;
    });
    mdContent += '\n';

    // Subjects
    mdContent += '## Subjects\n\n';
    educationData.subjects.forEach((subject) => {
      mdContent += `- ${subject.name}\n`;
    });
    mdContent += '\n';

    // Streams
    mdContent += '## Streams\n\n';
    educationData.streams.forEach((stream) => {
      mdContent += `- ${stream.name}\n`;
    });
    mdContent += '\n';

    // Degree Levels
    mdContent += '## Degree Levels\n\n';
    educationData.degreeLevels.forEach((level) => {
      mdContent += `- ${level.name}\n`;
    });
    mdContent += '\n';

    // Degrees
    mdContent += '## Degrees\n\n';
    educationData.degrees.forEach((degree) => {
      mdContent += `- ${degree.name}\n`;
    });
    mdContent += '\n';

    // Branches
    mdContent += '## Branches\n\n';
    educationData.branches.forEach((branch) => {
      mdContent += `- ${branch.name}\n`;
    });
    mdContent += '\n';

    // Language Types
    mdContent += '## Language Types\n\n';
    educationData.languageTypes.forEach((type) => {
      mdContent += `- ${type.name}\n`;
    });
    mdContent += '\n';

    // Languages
    mdContent += '## Languages\n\n';
    educationData.languages.forEach((language) => {
      mdContent += `- ${language.name}\n`;
    });
    mdContent += '\n';

    // Hobby Types
    mdContent += '## Hobby Types\n\n';
    educationData.hobbyTypes.forEach((type) => {
      mdContent += `- ${type.name}\n`;
    });
    mdContent += '\n';

    // Hobbies
    mdContent += '## Hobbies\n\n';
    educationData.hobbies.forEach((hobby) => {
      mdContent += `- ${hobby.name}\n`;
    });
    mdContent += '\n';

    // Course Types
    mdContent += '## Course Types\n\n';
    educationData.courseTypes.forEach((type) => {
      mdContent += `- ${type.name}\n`;
    });
    mdContent += '\n';

    // Courses
    mdContent += '## Courses\n\n';
    educationData.courses.forEach((course) => {
      mdContent += `- ${course.name}\n`;
    });
    mdContent += '\n';

    // Exam Categories
    mdContent += '## Exam Categories\n\n';
    educationData.examCategories.forEach((category) => {
      mdContent += `- ${category.name}\n`;
    });
    mdContent += '\n';

    // Exams
    mdContent += '## Exams\n\n';
    educationData.exams.forEach((exam) => {
      mdContent += `- ${exam.name}\n`;
    });
    mdContent += '\n';

    // Exam Subjects
    mdContent += '## Exam Subjects\n\n';
    educationData.examSubjects.forEach((subject) => {
      mdContent += `- ${subject.name}\n`;
    });

    fs.writeFileSync(mdFilePath, mdContent);
    console.log(`Education data written to ${mdFilePath}`);
  } catch (error) {
    console.error('Error writing education data to file:', error);
  }
}

// Main setup function
async function setupEducation() {
  try {
    console.log('Starting education setup...');

    // Connect to MongoDB
    await dbConnect(mongoUri);
    console.log('Connected to MongoDB');

    // Setup service categories
    await setupServiceCategories();

    // Setup school data
    await setupSchools();

    // Setup college data
    await setupColleges();

    // Setup language data
    await setupLanguages();

    // Setup hobby data
    await setupHobbies();

    // Setup IT course data
    await setupCourses();

    // Setup exam data
    await setupExams();

    // Write education data to file
    await writeEducationDataToFile();

    console.log('Education setup completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error setting up education data:', error);
    process.exit(1);
  }
}

// Setup service categories
async function setupServiceCategories() {
  console.log('Setting up service categories...');

  // Delete all existing service categories
  await ServiceCategory.deleteMany({});

  // Create new service categories
  for (const category of Object.values(serviceCategoryMap)) {
    const newCategory = await ServiceCategory.create({
      name: category.key,
      isActive: true,
    });

    // Add to data collection
    educationData.serviceCategories.push({ name: category.label });
  }

  console.log('Service categories setup completed');
}

// Setup school data
async function setupSchools() {
  console.log('Setting up school data...');

  // Delete all existing school data
  await Promise.all([Board.deleteMany({}), Class.deleteMany({}), Subject.deleteMany({})]);

  // Create boards
  const boards = await Promise.all(
    Object.values(boardMap).map((board) => {
      // Add to data collection
      educationData.boards.push({ name: board.label });

      return Board.create({
        name: board.key,
        isActive: true,
      });
    })
  );

  // Create classes for each board
  for (const board of boards) {
    await Promise.all(
      Object.values(classMap).map((classItem) => {
        // Add to data collection
        educationData.classes.push({ name: classItem.key });

        return Class.create({
          name: classItem.key,
          displayOrder: classItem.displayOrder,
          board: board._id,
          isActive: true,
        });
      })
    );
  }

  // Get all boards and classes
  const allBoards = await Board.find({});
  const allClasses = await Class.find({});

  // Create subjects for each board and class combination
  for (const board of allBoards) {
    const boardClasses = allClasses.filter((cls) => cls.board.toString() === board._id.toString());

    for (const cls of boardClasses) {
      // Create subjects for this board and class combination
      await Promise.all(
        Object.values(subjectMap).map((subject) => {
          // Add to data collection (only once per subject)
          if (!educationData.subjects.some((s) => s.name === subject.key)) {
            educationData.subjects.push({ name: subject.key });
          }

          return Subject.create({
            name: subject.key,
            board: board._id,
            class: cls._id,
            isActive: true,
          });
        })
      );

      console.log(`Created subjects for ${board.name} - ${cls.name}`);
    }
  }

  console.log('School data setup completed');
}

// Setup college data
async function setupColleges() {
  console.log('Setting up college data...');

  // Delete all existing college data
  await Promise.all([Stream.deleteMany({}), DegreeLevel.deleteMany({}), Degree.deleteMany({}), Branch.deleteMany({})]);

  // Create streams
  const streams = await Promise.all(
    Object.values(streamMap).map((stream) => {
      // Add to data collection
      educationData.streams.push({ name: stream.key });

      return Stream.create({
        name: stream.key,
        isActive: true,
      });
    })
  );

  // Create degree levels for each stream
  const degreeLevels = [];
  for (const stream of streams) {
    const streamDegreeLevels = await Promise.all(
      Object.values(degreeLevelMap).map((degreeLevel) => {
        // Add to data collection (only once per degree level)
        if (!educationData.degreeLevels.some((dl) => dl.name === degreeLevel.key)) {
          educationData.degreeLevels.push({ name: degreeLevel.key });
        }

        return DegreeLevel.create({
          name: degreeLevel.key,
          stream: stream._id,
          isActive: true,
        });
      })
    );
    degreeLevels.push(...streamDegreeLevels);
  }

  // Create degrees for Engineering stream
  const engineeringStream = streams.find((stream) => stream.name === streamMap.engineering.key);
  const engineeringGraduation = degreeLevels.find(
    (level) => level.name === degreeLevelMap.graduation.key && level.stream.toString() === engineeringStream?._id.toString()
  );

  if (engineeringStream && engineeringGraduation) {
    const engineeringDegrees = [{ key: 'BTech' }, { key: 'BE' }];

    const degrees = await Promise.all(
      engineeringDegrees.map((degree) => {
        // Add to data collection
        educationData.degrees.push({ name: degree.key });

        return Degree.create({
          name: degree.key,
          stream: engineeringStream._id,
          degreeLevel: engineeringGraduation._id,
          isActive: true,
        });
      })
    );

    // Create branches for BTech
    const btechDegree = degrees.find((degree) => degree.name === 'BTech');
    if (btechDegree) {
      const branches = [
        { key: 'Computer Science' },
        { key: 'Information Technology' },
        { key: 'Electronics & Communication' },
        { key: 'Electrical Engineering' },
        { key: 'Mechanical Engineering' },
        { key: 'Civil Engineering' },
        { key: 'Chemical Engineering' },
      ];

      await Promise.all(
        branches.map((branch) => {
          // Add to data collection
          educationData.branches.push({ name: branch.key });

          return Branch.create({
            name: branch.key,
            degree: btechDegree._id,
            isActive: true,
          });
        })
      );
    }
  }

  // Create degrees for Medical stream
  const medicalStream = streams.find((stream) => stream.name === streamMap.medical.key);
  const medicalGraduation = degreeLevels.find(
    (level) => level.name === degreeLevelMap.graduation.key && level.stream.toString() === medicalStream?._id.toString()
  );

  if (medicalStream && medicalGraduation) {
    const medicalDegrees = [{ key: 'MBBS' }, { key: 'BDS' }, { key: 'BAMS' }];

    await Promise.all(
      medicalDegrees.map((degree) => {
        // Add to data collection
        educationData.degrees.push({ name: degree.key });

        return Degree.create({
          name: degree.key,
          stream: medicalStream._id,
          degreeLevel: medicalGraduation._id,
          isActive: true,
        });
      })
    );
  }

  console.log('College data setup completed');
}

// Setup language data
async function setupLanguages() {
  console.log('Setting up language data...');

  // Delete all existing language data
  await Promise.all([LanguageType.deleteMany({}), Language.deleteMany({})]);

  // Create language types
  const languageTypes = await Promise.all(
    Object.values(languageTypeMap).map((type) => {
      // Add to data collection
      educationData.languageTypes.push({ name: type.key });

      return LanguageType.create({
        name: type.key,
        isActive: true,
      });
    })
  );

  // Create languages for each type
  const indianLanguageType = languageTypes.find((type) => type.name === languageTypeMap.indian.key);
  const foreignLanguageType = languageTypes.find((type) => type.name === languageTypeMap.foreign.key);

  if (indianLanguageType) {
    const indianLanguages = [
      { key: 'Hindi' },
      { key: 'Bengali' },
      { key: 'Telugu' },
      { key: 'Marathi' },
      { key: 'Tamil' },
      { key: 'Urdu' },
      { key: 'Gujarati' },
      { key: 'Kannada' },
      { key: 'Malayalam' },
      { key: 'Punjabi' },
    ];

    await Promise.all(
      indianLanguages.map((language) => {
        // Add to data collection
        educationData.languages.push({ name: language.key });

        return Language.create({
          name: language.key,
          languageType: indianLanguageType._id,
          isActive: true,
        });
      })
    );
  }

  if (foreignLanguageType) {
    const foreignLanguages = [
      { key: 'English' },
      { key: 'French' },
      { key: 'German' },
      { key: 'Spanish' },
      { key: 'Japanese' },
      { key: 'Chinese' },
      { key: 'Russian' },
      { key: 'Arabic' },
      { key: 'Portuguese' },
      { key: 'Italian' },
    ];

    await Promise.all(
      foreignLanguages.map((language) => {
        // Add to data collection
        educationData.languages.push({ name: language.key });

        return Language.create({
          name: language.key,
          languageType: foreignLanguageType._id,
          isActive: true,
        });
      })
    );
  }

  console.log('Language data setup completed');
}

// Setup hobby data
async function setupHobbies() {
  console.log('Setting up hobby data...');

  // Delete all existing hobby data
  await Promise.all([HobbyType.deleteMany({}), Hobby.deleteMany({})]);

  // Create hobby types
  const hobbyTypes = await Promise.all(
    Object.values(hobbyTypeMap).map((type) => {
      // Add to data collection
      educationData.hobbyTypes.push({ name: type.key });

      return HobbyType.create({
        name: type.key,
        isActive: true,
      });
    })
  );

  // Create hobbies for each type
  const musicType = hobbyTypes.find((type) => type.name === hobbyTypeMap.music.key);
  const danceType = hobbyTypes.find((type) => type.name === hobbyTypeMap.dance.key);
  const artType = hobbyTypes.find((type) => type.name === hobbyTypeMap.art.key);

  if (musicType) {
    const musicHobbies = [{ key: 'Guitar' }, { key: 'Piano' }, { key: 'Violin' }, { key: 'Drums' }, { key: 'Flute' }, { key: 'Vocal' }];

    await Promise.all(
      musicHobbies.map((hobby) => {
        // Add to data collection
        educationData.hobbies.push({ name: hobby.key });

        return Hobby.create({
          name: hobby.key,
          hobbyType: musicType._id,
          isActive: true,
        });
      })
    );
  }

  if (danceType) {
    const danceHobbies = [{ key: 'Classical' }, { key: 'Contemporary' }, { key: 'Hip Hop' }, { key: 'Bollywood' }, { key: 'Ballet' }];

    await Promise.all(
      danceHobbies.map((hobby) => {
        // Add to data collection
        educationData.hobbies.push({ name: hobby.key });

        return Hobby.create({
          name: hobby.key,
          hobbyType: danceType._id,
          isActive: true,
        });
      })
    );
  }

  if (artType) {
    const artHobbies = [{ key: 'Painting' }, { key: 'Sketching' }, { key: 'Pottery' }, { key: 'Origami' }, { key: 'Calligraphy' }];

    await Promise.all(
      artHobbies.map((hobby) => {
        // Add to data collection
        educationData.hobbies.push({ name: hobby.key });

        return Hobby.create({
          name: hobby.key,
          hobbyType: artType._id,
          isActive: true,
        });
      })
    );
  }

  console.log('Hobby data setup completed');
}

// Setup IT course data
async function setupCourses() {
  console.log('Setting up IT course data...');

  // Delete all existing course data
  await Promise.all([CourseType.deleteMany({}), Course.deleteMany({})]);

  // Create course types
  const courseTypes = await Promise.all(
    Object.values(courseTypeMap).map((type) => {
      // Add to data collection
      educationData.courseTypes.push({ name: type.key });

      return CourseType.create({
        name: type.key,
        isActive: true,
      });
    })
  );

  // Create courses for each type
  const programmingType = courseTypes.find((type) => type.name === courseTypeMap.programming.key);
  const designType = courseTypes.find((type) => type.name === courseTypeMap.design.key);
  const webDevType = courseTypes.find((type) => type.name === courseTypeMap.web_development.key);
  const mobileDevType = courseTypes.find((type) => type.name === courseTypeMap.mobile_development.key);
  const dataScienceType = courseTypes.find((type) => type.name === courseTypeMap.data_science.key);

  if (programmingType) {
    const programmingCourses = [{ key: 'Python' }, { key: 'Java' }, { key: 'C++' }, { key: 'JavaScript' }, { key: 'C#' }];

    await Promise.all(
      programmingCourses.map((course) => {
        // Add to data collection
        educationData.courses.push({ name: course.key });

        return Course.create({
          name: course.key,
          courseType: programmingType._id,
          isActive: true,
        });
      })
    );
  }

  if (designType) {
    const designCourses = [{ key: 'Photoshop' }, { key: 'Illustrator' }, { key: 'UI/UX Design' }, { key: 'Graphic Design' }, { key: 'Animation' }];

    await Promise.all(
      designCourses.map((course) => {
        // Add to data collection
        educationData.courses.push({ name: course.key });

        return Course.create({
          name: course.key,
          courseType: designType._id,
          isActive: true,
        });
      })
    );
  }

  if (webDevType) {
    const webDevCourses = [
      { key: 'HTML/CSS' },
      { key: 'React.js' },
      { key: 'Angular' },
      { key: 'Vue.js' },
      { key: 'Node.js' },
      { key: 'PHP' },
      { key: 'Django' },
    ];

    await Promise.all(
      webDevCourses.map((course) => {
        // Add to data collection
        educationData.courses.push({ name: course.key });

        return Course.create({
          name: course.key,
          courseType: webDevType._id,
          isActive: true,
        });
      })
    );
  }

  if (mobileDevType) {
    const mobileDevCourses = [{ key: 'Flutter' }, { key: 'React Native' }, { key: 'Swift' }, { key: 'Kotlin' }, { key: 'Xamarin' }];

    await Promise.all(
      mobileDevCourses.map((course) => {
        // Add to data collection
        educationData.courses.push({ name: course.key });

        return Course.create({
          name: course.key,
          courseType: mobileDevType._id,
          isActive: true,
        });
      })
    );
  }

  if (dataScienceType) {
    const dataScienceCourses = [
      { key: 'Python for Data Science' },
      { key: 'R Programming' },
      { key: 'Machine Learning' },
      { key: 'Deep Learning' },
      { key: 'Data Visualization' },
      { key: 'Big Data Analytics' },
    ];

    await Promise.all(
      dataScienceCourses.map((course) => {
        // Add to data collection
        educationData.courses.push({ name: course.key });

        return Course.create({
          name: course.key,
          courseType: dataScienceType._id,
          isActive: true,
        });
      })
    );
  }

  console.log('IT course data setup completed');
}

// Setup exam data
async function setupExams() {
  console.log('Setting up exam data...');

  // Delete all existing exam data
  await Promise.all([ExamCategory.deleteMany({}), Exam.deleteMany({}), ExamSubject.deleteMany({})]);

  // Create exam categories
  const examCategories = await Promise.all(
    Object.values(examCategoryMap).map((category) => {
      // Add to data collection
      educationData.examCategories.push({ name: category.key });

      return ExamCategory.create({
        name: category.key,
        isActive: true,
      });
    })
  );

  // Create exams for each category
  const competitiveCategory = examCategories.find((category) => category.name === examCategoryMap.competitive.key);
  const entranceCategory = examCategories.find((category) => category.name === examCategoryMap.entrance.key);

  if (competitiveCategory) {
    const competitiveExams = [{ key: 'UPSC' }, { key: 'SSC' }, { key: 'Banking' }, { key: 'Railways' }, { key: 'Defence' }];

    const createdExams = await Promise.all(
      competitiveExams.map((exam) => {
        // Add to data collection
        educationData.exams.push({ name: exam.key });

        return Exam.create({
          name: exam.key,
          examCategory: competitiveCategory._id,
          isActive: true,
        });
      })
    );

    // Create subjects for UPSC
    const upscExam = createdExams.find((exam) => exam.name === 'UPSC');
    if (upscExam) {
      const upscSubjects = [{ key: 'General Studies' }, { key: 'CSAT' }, { key: 'Essay' }, { key: 'Optional Subject' }];

      await Promise.all(
        upscSubjects.map((subject) => {
          // Add to data collection
          educationData.examSubjects.push({ name: subject.key });

          return ExamSubject.create({
            name: subject.key,
            exam: upscExam._id,
            isActive: true,
          });
        })
      );
    }
  }

  if (entranceCategory) {
    const entranceExams = [{ key: 'JEE' }, { key: 'NEET' }, { key: 'CLAT' }, { key: 'CAT' }, { key: 'GATE' }];

    const createdExams = await Promise.all(
      entranceExams.map((exam) => {
        // Add to data collection
        educationData.exams.push({ name: exam.key });

        return Exam.create({
          name: exam.key,
          examCategory: entranceCategory._id,
          isActive: true,
        });
      })
    );

    // Create subjects for JEE
    const jeeExam = createdExams.find((exam) => exam.name === 'JEE');
    if (jeeExam) {
      const jeeSubjects = [{ key: 'Physics' }, { key: 'Chemistry' }, { key: 'Mathematics' }];

      await Promise.all(
        jeeSubjects.map((subject) => {
          // Add to data collection (only once per subject)
          if (!educationData.examSubjects.some((s) => s.name === subject.key)) {
            educationData.examSubjects.push({ name: subject.key });
          }

          return ExamSubject.create({
            name: subject.key,
            exam: jeeExam._id,
            isActive: true,
          });
        })
      );
    }

    // Create subjects for NEET
    const neetExam = createdExams.find((exam) => exam.name === 'NEET');
    if (neetExam) {
      const neetSubjects = [{ key: 'Physics' }, { key: 'Chemistry' }, { key: 'Biology' }];

      await Promise.all(
        neetSubjects.map((subject) => {
          // Add to data collection (only once per subject)
          if (!educationData.examSubjects.some((s) => s.name === subject.key)) {
            educationData.examSubjects.push({ name: subject.key });
          }

          return ExamSubject.create({
            name: subject.key,
            exam: neetExam._id,
            isActive: true,
          });
        })
      );
    }
  }

  console.log('Exam data setup completed');
}

// Run the setup function
setupEducation();
