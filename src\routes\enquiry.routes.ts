import express from 'express';
import { searchEducationalServices, createEnquiry, getCategoryItems } from '@/controllers/enquiry.controller';
import { getParentEnquiries, getParentEnquiryById, updateEnquiryStatus } from '@/controllers/parent-enquiry.controller';
import { authenticatedUser } from '@/middleware/auth.middleware';

const router = express.Router();

// Step 1: Educational Service Selection
router.post('/search', searchEducationalServices);
// Step 2: Get Education Category Items
router.get('/category/:type', getCategoryItems);

// Create Enquiry
router.post('/create', authenticatedUser, createEnquiry);

// Parent Enquiry Routes
router.get('/parent', authenticatedUser, getParentEnquiries);
router.get('/parent/:id', authenticatedUser, getParentEnquiryById);
router.patch('/parent/:id/status', authenticatedUser, updateEnquiryStatus);

export default router;
