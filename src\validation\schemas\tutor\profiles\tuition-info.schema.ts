import { z } from 'zod';
import { deliveryModeMap, IDeliveryModeMap } from '../../enquiry.maps';
import { coordinatesSchema } from '../../common.schema';

export const createTuitionInfoSchema = z.object({
  // Required fields
  totalTeachingExperience: z.number().min(0, 'Teaching experience must be non-negative').describe('Total teaching experience in months'),
  isFullTimeTeacher: z.boolean().describe('Whether the tutor is a full-time teacher'),
  teachesSpecialStudents: z.boolean().describe('Whether the tutor teaches special students'),
  maxTravelDistance: z.number().min(0, 'Travel distance must be non-negative').describe('Maximum travel distance in kilometers'),
  spokenLanguages: z.array(z.string()).min(1, 'At least one language must be selected').describe('Languages spoken by the tutor'),
  deliveryModes: z
    .array(z.enum(Object.keys(deliveryModeMap) as [IDeliveryModeMap, ...IDeliveryModeMap[]]))
    .min(1, 'At least one delivery mode must be selected')
    .describe('Available delivery modes for teaching'),
  location: z.string().trim().min(1, 'Location is required').describe('Primary teaching location'),
  coordinates: coordinatesSchema.optional().describe('Geographical coordinates of the location'),
  description: z
    .string()
    .trim()
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description cannot exceed 1000 characters')
    .describe('Description of teaching experience and approach'),
});

export const updateTuitionInfoSchema = createTuitionInfoSchema.partial();

export type CreateTuitionInfoInput = z.infer<typeof createTuitionInfoSchema>;
export type UpdateTuitionInfoInput = z.infer<typeof updateTuitionInfoSchema>;
