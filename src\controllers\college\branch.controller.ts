import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Branch from '@/models/college/branch.model';
import Degree from '@/models/college/degree.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createBranchSchema, UpdateBranchInput } from '@/validation/schemas/education/college.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const branchAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'degrees',
      localField: 'degree',
      foreignField: '_id',
      as: 'degreeDetails',
    },
  },
  {
    $addFields: {
      degreeDetails: { $arrayElemAt: ['$degreeDetails', 0] },
    },
  },
  {
    $lookup: {
      from: 'streams',
      localField: 'degreeDetails.stream',
      foreignField: '_id',
      as: 'streamDetails',
    },
  },
  {
    $lookup: {
      from: 'degreelevels',
      localField: 'degreeDetails.degreeLevel',
      foreignField: '_id',
      as: 'degreeLevelDetails',
    },
  },
  {
    $addFields: {
      streamDetails: { $arrayElemAt: ['$streamDetails', 0] },
      degreeLevelDetails: { $arrayElemAt: ['$degreeLevelDetails', 0] },
    },
  },
  { $sort: { name: 1 } },
];

/**
 * Create a new branch
 * @route POST /api/v1/education/branches
 * @access Private (Admin)
 */
export const createBranch = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createBranchSchema, req.body);
  const { name, degree } = validatedData;

  if (!isValidObjectId(degree)) {
    throw new BadRequestError('Please provide a valid degree ID');
  }

  const degreeExists = await Degree.findById(degree);
  if (!degreeExists) {
    throw new NotFoundError(`No degree found with id: ${degree}`);
  }

  // Check if a branch with the same name and degree already exists
  const existingBranch = await Branch.findOne({ name, degree });
  if (existingBranch) {
    throw new BadRequestError(`A branch with the name '${name}' already exists for this degree`);
  }

  const branch = await Branch.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Branch created successfully',
    data: { branch },
  });
};

/**
 * Get all branches
 * @route GET /api/v1/education/branches
 * @access Public
 */
export const getAllBranches = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.degree && isValidObjectId(queryString.degree)) {
    matchStage['degree'] = new mongoose.Types.ObjectId(queryString.degree as string);
    delete queryString.degree;
  }

  const baseAggregation = [{ $match: matchStage }, ...branchAggregation];

  const queryManager = new AggregateQueryManager({
    model: Branch,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [branches, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Branches fetched successfully',
    data: { branches, pagination },
  });
};

/**
 * Get a branch by ID
 * @route GET /api/v1/education/branches/:id
 * @access Public
 */
export const getBranchById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid branch ID');
  }

  const [branch] = await Branch.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...branchAggregation,
  ]);

  if (!branch) {
    throw new NotFoundError(`No branch found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Branch fetched successfully',
    data: { branch },
  });
};

/**
 * Update a branch
 * @route PATCH /api/v1/education/branches/:id
 * @access Private (Admin)
 */
export const updateBranch = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateBranchInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid branch ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const branch = await Branch.findById(id);
  if (!branch) {
    throw new NotFoundError(`No branch found with id: ${id}`);
  }

  // Validate degree if provided
  if (updateData.degree && !isValidObjectId(updateData.degree)) {
    throw new BadRequestError('Please provide a valid degree ID');
  }

  if (updateData.degree) {
    const degreeExists = await Degree.findById(updateData.degree);
    if (!degreeExists) {
      throw new NotFoundError(`No degree found with id: ${updateData.degree}`);
    }
  }

  // Check for duplicate if name or degree is being updated
  if (updateData.name || updateData.degree) {
    const name = updateData.name || branch.name;
    const degree = updateData.degree || branch.degree;

    const existingBranch = await Branch.findOne({
      name,
      degree,
      _id: { $ne: id },
    });

    if (existingBranch) {
      throw new BadRequestError(`A branch with the name '${name}' already exists for this degree`);
    }
  }

  await Branch.findByIdAndUpdate(id, updateData, { runValidators: true });

  const [updatedBranch] = await Branch.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...branchAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Branch updated successfully',
    data: { branch: updatedBranch },
  });
};

/**
 * Delete a branch
 * @route DELETE /api/v1/education/branches/:id
 * @access Private (Super Admin)
 */
export const deleteBranch = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid branch ID');
  }

  const branch = await Branch.findById(id);
  if (!branch) {
    throw new NotFoundError(`No branch found with id: ${id}`);
  }

  // TODO: Check if there are any subjects associated with this branch
  // If yes, prevent deletion or implement cascade delete based on requirements

  await Branch.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Branch deleted successfully',
    data: null,
  });
};
