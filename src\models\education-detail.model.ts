import mongoose, { Document, Schema } from 'mongoose';
import { IEducationTypeMap, IScoreTypeMap, educationTypeMap, scoreTypeMap } from '@/validation/schemas/parent/education.maps';

export interface IEducationDetail {
  type: 'tutor' | 'student'; // Type of profile - tutor or student
  userId: mongoose.Types.ObjectId; // Reference to user (always present - parent for students, tutor for tutors)
  childProfileId?: mongoose.Types.ObjectId; // Reference to child profile (for students only)
  educationType: IEducationTypeMap;

  // School specific fields
  boardId?: mongoose.Types.ObjectId;
  classId?: mongoose.Types.ObjectId;
  schoolName?: string;

  // Degree specific fields
  streamId?: mongoose.Types.ObjectId;
  degreeLevelId?: mongoose.Types.ObjectId;
  degreeId?: mongoose.Types.ObjectId;
  branchId?: mongoose.Types.ObjectId;
  collegeName?: string;

  // Other achievement specific fields
  certificateName?: string;
  certificateFor?: string;
  certificateBy?: string;

  // Common fields
  location: string;
  businessLocationId?: mongoose.Types.ObjectId;
  startDate: Date;
  endDate?: Date;
  scoreType: IScoreTypeMap;
  obtainedValue: string;
  maximumValue: number;
  certificateNumber?: string;
  attachmentUrl?: string;
  isPursuing: boolean;
  isActive: boolean;
}

export interface EducationDetailDocument extends IEducationDetail, Document {
  createdAt: Date;
  updatedAt: Date;
}

const educationDetailSchema = new Schema<EducationDetailDocument>(
  {
    type: {
      type: String,
      enum: ['tutor', 'student'],
      required: [true, 'Type is required'],
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    childProfileId: {
      type: Schema.Types.ObjectId,
      ref: 'ChildProfile',
      required: function () {
        return this.type === 'student';
      },
    },
    educationType: {
      type: String,
      enum: Object.keys(educationTypeMap),
      required: [true, 'Education type is required'],
    },

    // School specific fields
    boardId: {
      type: Schema.Types.ObjectId,
      ref: 'Board',
      required: function () {
        return this.educationType === 'school';
      },
    },
    classId: {
      type: Schema.Types.ObjectId,
      ref: 'Class',
      required: function () {
        return this.educationType === 'school';
      },
    },
    schoolName: {
      type: String,
      required: function () {
        return this.educationType === 'school';
      },
    },

    // Degree specific fields
    streamId: {
      type: Schema.Types.ObjectId,
      ref: 'Stream',
      required: function () {
        return this.educationType === 'degree';
      },
    },
    degreeLevelId: {
      type: Schema.Types.ObjectId,
      ref: 'DegreeLevel',
      required: function () {
        return this.educationType === 'degree';
      },
    },
    degreeId: {
      type: Schema.Types.ObjectId,
      ref: 'Degree',
      required: function () {
        return this.educationType === 'degree';
      },
    },
    branchId: {
      type: Schema.Types.ObjectId,
      ref: 'Branch',
      required: function () {
        return this.educationType === 'degree';
      },
    },
    collegeName: {
      type: String,
      required: function () {
        return this.educationType === 'degree';
      },
    },

    // Other achievement specific fields
    certificateName: {
      type: String,
      required: function () {
        return this.educationType === 'other';
      },
    },
    certificateFor: {
      type: String,
      required: function () {
        return this.educationType === 'other';
      },
    },
    certificateBy: {
      type: String,
      required: function () {
        return this.educationType === 'other';
      },
    },

    // Common fields
    location: {
      type: String,
      required: [true, 'Location is required'],
    },
    businessLocationId: {
      type: Schema.Types.ObjectId,
      ref: 'BusinessLocation',
    },
    startDate: { type: Date, required: [true, 'Start date is required'] },
    endDate: { type: Date },
    scoreType: {
      type: String,
      enum: Object.keys(scoreTypeMap),
      required: function (this: EducationDetailDocument) {
        return !this.isPursuing;
      },
    },
    obtainedValue: {
      type: String,
      required: function (this: EducationDetailDocument) {
        return !this.isPursuing;
      },
    },
    maximumValue: {
      type: Number,
      required: function (this: EducationDetailDocument) {
        return !this.isPursuing;
      },
    },
    certificateNumber: {
      type: String,
    },
    attachmentUrl: {
      type: String,
    },
    isPursuing: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Add indexes to optimize queries
educationDetailSchema.index({ type: 1 });
educationDetailSchema.index({ userId: 1 });
educationDetailSchema.index({ childProfileId: 1 });
educationDetailSchema.index({ educationType: 1 });
educationDetailSchema.index({ isActive: 1 });
educationDetailSchema.index({ type: 1, userId: 1 });
educationDetailSchema.index({ type: 1, childProfileId: 1 });

// Add indexes for reference fields
educationDetailSchema.index({ boardId: 1 });
educationDetailSchema.index({ classId: 1 });
educationDetailSchema.index({ streamId: 1 });
educationDetailSchema.index({ degreeLevelId: 1 });
educationDetailSchema.index({ degreeId: 1 });
educationDetailSchema.index({ branchId: 1 });

const EducationDetail = mongoose.models.EducationDetail || mongoose.model<EducationDetailDocument>('EducationDetail', educationDetailSchema);

export default EducationDetail;
