# IT Courses Service Category Documentation

## Overview

The IT Courses service category is one of the seven main educational domains in the platform. It provides a structured way to organize technology and computer science related educational content and services.

## Hierarchy

The IT Courses service category follows a two-level hierarchy:

1. **Course Type** - The category of IT course (e.g., Web Development, App Development)
2. **Course** - The specific course within a category (e.g., React.js, Flutter)

```
IT Courses
├── Course Type (Web Development, App Development, Data Science, etc.)
│   ├── Course (React.js, Flutter, Python for Data Science, etc.)
```

## Data Models

### Course Type Model

The Course Type model represents categories of IT courses.

```typescript
// src/models/course/course-type.model.ts
export interface ICourseType {
  name: ICourseTypeMap; // Enum: 'web_development', 'app_development', etc.
  isActive: boolean;
}

// Schema definition
const courseTypeSchema = new Schema<CourseTypeDocument>({
  name: {
    type: String,
    enum: Object.keys(courseTypeMap),
    required: [true, 'Course type name is required'],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
courseTypeSchema.index({ name: 1 });
```

### Course Model

The Course model represents specific courses within a course type.

```typescript
// src/models/course/course.model.ts
export interface ICourse {
  name: string;
  courseType: mongoose.Types.ObjectId; // Reference to CourseType
  isActive: boolean;
}

// Schema definition
const courseSchema = new Schema<CourseDocument>({
  name: {
    type: String,
    required: [true, 'Course name is required'],
    trim: true,
  },
  courseType: {
    type: Schema.Types.ObjectId,
    ref: 'CourseType',
    required: [true, 'Course type is required'],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
courseSchema.index({ courseType: 1, name: 1 }, { unique: true });
```

## API Endpoints

### Course Type Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/course-types` | Get all course types | No |
| GET | `/api/v1/education/course-types/:id` | Get a specific course type | No |
| POST | `/api/v1/education/course-types` | Create a new course type | Yes (admin) |
| PATCH | `/api/v1/education/course-types/:id` | Update a course type | Yes (admin) |
| DELETE | `/api/v1/education/course-types/:id` | Delete a course type | Yes (super_admin) |

### Course Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/courses` | Get all courses | No |
| GET | `/api/v1/education/courses/type/:courseTypeId` | Get courses by type | No |
| GET | `/api/v1/education/courses/:id` | Get a specific course | No |
| POST | `/api/v1/education/courses` | Create a new course | Yes (admin) |
| PATCH | `/api/v1/education/courses/:id` | Update a course | Yes (admin) |
| DELETE | `/api/v1/education/courses/:id` | Delete a course | Yes (super_admin) |

## Predefined Data

### Course Types

The system comes with predefined course types:

- Web Development
- App Development
- Data Science
- Cloud Computing
- Cybersecurity
- Other

### Example Courses

#### Web Development
- HTML/CSS
- JavaScript
- React.js
- Angular
- Vue.js
- Node.js
- PHP
- Django

#### App Development
- Flutter
- React Native
- Swift
- Kotlin
- Xamarin

#### Data Science
- Python for Data Science
- R Programming
- Machine Learning
- Deep Learning
- Data Visualization
- Big Data Analytics

#### Cloud Computing
- AWS
- Azure
- Google Cloud
- Docker
- Kubernetes

#### Cybersecurity
- Network Security
- Ethical Hacking
- Cryptography
- Security Auditing
- Penetration Testing

## Data Flow

1. **Creating a Course Type**: Admin creates a course type (e.g., Web Development)
2. **Creating Courses**: Admin creates courses within that type (e.g., React.js, Node.js)

## Usage Examples

### Creating a Course Type

```javascript
// POST /api/v1/education/course-types
{
  "name": "web_development"
}
```

### Creating a Course

```javascript
// POST /api/v1/education/courses
{
  "name": "React.js",
  "courseType": "60d5ec9af682d123e4567890" // Course Type ID
}
```

### Getting Courses by Type

```
GET /api/v1/education/courses/type/60d5ec9af682d123e4567890
```

## Relationships with Other Entities

- **Users**: Tutors can specify which IT courses they teach
- **Students**: Students can express interest in specific IT courses
- **Content**: Educational content can be categorized by course
- **Classes/Workshops**: Technical classes and workshops can be organized by course

## Implementation Notes

- The hierarchy is enforced through validation in the controllers
- When creating a course, the course type must exist
- All entities have an `isActive` flag to enable/disable without deletion
- The course catalog can be regularly updated to reflect current technology trends
