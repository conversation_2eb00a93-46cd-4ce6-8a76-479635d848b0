import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { NotFoundError, UnauthenticatedError } from '@/errors';
import Staff from '@/models/staff.model';
import StaffSession from '@/models/staff-session.model';
import { createStaffSessionCookie, createStaffSessionUser, validateData, updateOrCreateStaffSession } from '@/utils';
import { AuthenticatedStaffRequest } from '@/types/express';
import { StaffLoginInput, staffLoginSchema, updateStaffPasswordSchema } from '@/validation/schemas/staff.schema';

export const staffLogin = async (req: Request<{}, {}, StaffLoginInput>, res: Response): Promise<void> => {
  const validatedData = await validateData(staffLoginSchema, req.body);
  const { email, password } = validatedData;

  const staff = await Staff.findOne({ email }).select('+password');
  if (!staff) {
    throw new UnauthenticatedError('Invalid credentials');
  }

  if (!staff.isActive) {
    throw new UnauthenticatedError('Your account has been deactivated. Please contact the administrator.');
  }

  const isPasswordValid = await staff.comparePassword(password);
  if (!isPasswordValid) {
    throw new UnauthenticatedError('Invalid credentials');
  }

  staff.lastLogin = new Date();
  await staff.save();

  const { sessionId } = await updateOrCreateStaffSession(staff._id, req);

  const sessionStaff = await createStaffSessionUser(staff);

  const token = await createStaffSessionCookie({ res, staff: sessionStaff, sessionId });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Login successful',
    data: {
      staff: sessionStaff,
      token,
    },
  });
};

export const staffLogout = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const staffId = req.staff?.staffId;
  const sessionId = req.sessionId;

  if (sessionId) {
    await StaffSession.updateOne({ sessionId, staff: staffId, isValid: true }, { isValid: false });
  } else {
    await StaffSession.updateMany({ staff: staffId, isValid: true }, { isValid: false });
  }

  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    signed: true,
    sameSite: process.env.NODE_ENV === 'production' ? ('strict' as const) : ('lax' as const),
    path: '/',
    maxAge: 0,
  };

  if (process.env.NODE_ENV === 'production') {
    const domain = process.env.APP_DOMAIN || '.perfecttutor.site';
    Object.assign(cookieOptions, { domain });
  }

  res.cookie('staff_session', 'logout', cookieOptions);

  res.status(StatusCodes.OK).json({ success: true, message: 'Logged out successfully!' });
};

export const updateStaffPassword = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const staffId = req.staff?.staffId;
  const validatedData = await validateData(updateStaffPasswordSchema, req.body);
  const { currentPassword, newPassword } = validatedData;

  const staff = await Staff.findById(staffId).select('+password');
  if (!staff) {
    throw new NotFoundError('Staff not found');
  }

  const isPasswordValid = await staff.comparePassword(currentPassword);
  if (!isPasswordValid) {
    throw new UnauthenticatedError('Current password is incorrect');
  }

  staff.password = newPassword;
  await staff.save();

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Password updated successfully',
  });
};

export const refreshStaffToken = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  try {
    if (!req.staff || !req.sessionId) {
      throw new UnauthenticatedError('Invalid authentication state');
    }

    const staffId = req.staff.staffId;
    const sessionId = req.sessionId;

    const session = await StaffSession.findOne({
      sessionId,
      staff: staffId,
      isValid: true,
    });

    if (!session) {
      throw new UnauthenticatedError('Session not found or invalidated');
    }

    if (session.expiresAt < new Date()) {
      throw new UnauthenticatedError('Session has expired');
    }

    const sessionLifetime = Number.parseInt(process.env.SESSION_MAX_AGE!, 10) || 604800000; // 7 days in ms
    const expiresAt = new Date(Date.now() + sessionLifetime);
    session.expiresAt = expiresAt;
    await session.save();

    const newToken = await createStaffSessionCookie({
      res,
      staff: req.staff,
      sessionId,
      returnTokenOnly: true,
    });

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token: newToken,
        staff: req.staff,
      },
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};
