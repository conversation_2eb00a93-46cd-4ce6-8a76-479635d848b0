import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { NotFoundError, BadRequestError } from '@/errors';
import Address from '@/models/address.model';
import User from '@/models/user.model';
import { validateData, updateAccountStatus, isProfileComplete } from '@/utils';
import { AuthenticatedRequest } from '@/types/express';
import { isValidObjectId } from 'mongoose';
import { addressSchema } from '@/validation/schemas/user.schema';

export const createAddress = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const validatedData = await validateData(addressSchema, req.body);

  const existingAddress = await Address.findOne({ user: userId, addressType: validatedData.addressType });

  if (existingAddress) throw new BadRequestError(`You already have a ${validatedData.addressType} address. Please update it instead.`);

  const address = await Address.create({ ...validatedData, user: userId });

  const user = await User.findById(userId);

  if (user) {
    const profileComplete = await isProfileComplete(user);
    await updateAccountStatus(user, { isProfileComplete: profileComplete });
    await user.save();
  }

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Address created successfully',
    data: { address },
  });
};

export const getUserAddresses = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;

  const addresses = await Address.find({ user: userId }).sort({ addressType: 1, updatedAt: -1 });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Addresses fetched successfully',
    data: { addresses },
  });
};

export const getAddressById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const { addressId } = req.params;

  if (!isValidObjectId(addressId)) throw new BadRequestError('Please provide a valid address ID');

  const address = await Address.findOne({ _id: addressId, user: userId });

  if (!address) throw new NotFoundError('No address found with provided details');

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Address fetched successfully',
    data: { address },
  });
};

export const updateAddress = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const { addressId } = req.params;
  const validatedData = await validateData(addressSchema, req.body);

  if (!isValidObjectId(addressId)) {
    throw new BadRequestError('Please provide a valid address ID');
  }

  const existingAddress = await Address.findOne({ _id: addressId, user: userId });

  if (!existingAddress) {
    throw new NotFoundError('No address found with provided details');
  }

  if (validatedData.addressType !== existingAddress.addressType) {
    const addressWithNewType = await Address.findOne({
      user: userId,
      addressType: validatedData.addressType,
      _id: { $ne: addressId },
    });

    if (addressWithNewType) {
      throw new BadRequestError(`You already have a ${validatedData.addressType} address. Please update it instead.`);
    }
  }

  const updatedAddress = await Address.findByIdAndUpdate(addressId, { ...validatedData }, { new: true, runValidators: true });

  const user = await User.findById(userId);
  if (user) {
    const profileComplete = await isProfileComplete(user);
    await updateAccountStatus(user, { isProfileComplete: profileComplete });
    await user.save();
  }

  res.status(StatusCodes.OK).json({ success: true, message: 'Address updated successfully', data: { address: updatedAddress } });
};

export const deleteAddress = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const { addressId } = req.params;

  if (!isValidObjectId(addressId)) throw new BadRequestError('Please provide a valid address ID');

  const address = await Address.findOneAndDelete({ _id: addressId, user: userId });

  if (!address) throw new NotFoundError('No address found with provided details');

  const user = await User.findById(userId);

  if (user) {
    const profileComplete = await isProfileComplete(user);
    await updateAccountStatus(user, { isProfileComplete: profileComplete });
    await user.save();
  }

  res.status(StatusCodes.OK).json({ success: true, message: 'Address deleted successfully' });
};
