import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import ExamSubject from '@/models/exam/exam-subject.model';
import Exam from '@/models/exam/exam.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createExamSubjectSchema, UpdateExamSubjectInput } from '@/validation/schemas/education/exam.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const examSubjectAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'exams',
      localField: 'exam',
      foreignField: '_id',
      as: 'examDetails',
    },
  },
  { $unwind: { path: '$examDetails', preserveNullAndEmptyArrays: true } },
  {
    $lookup: {
      from: 'examcategories',
      localField: 'examDetails.examCategory',
      foreignField: '_id',
      as: 'examCategoryDetails',
    },
  },
  { $unwind: { path: '$examCategoryDetails', preserveNullAndEmptyArrays: true } },
  { $sort: { 'examCategoryDetails.name': 1, 'examDetails.name': 1, name: 1 } },
];

/**
 * Create a new exam subject
 * @route POST /api/v1/education/exam-subjects
 * @access Admin only
 */
export const createExamSubject = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createExamSubjectSchema, req.body);
  const { name, exam } = validatedData;

  if (!isValidObjectId(exam)) {
    throw new BadRequestError('Please provide a valid exam ID');
  }

  const examExists = await Exam.findById(exam);
  if (!examExists) {
    throw new NotFoundError(`No exam found with id: ${exam}`);
  }

  const existingExamSubject = await ExamSubject.findOne({ name, exam });
  if (existingExamSubject) {
    throw new BadRequestError(`An exam subject with the name '${name}' already exists for this exam`);
  }

  const examSubject = await ExamSubject.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Exam subject created successfully',
    data: { examSubject },
  });
};

/**
 * Get all exam subjects
 * @route GET /api/v1/education/exam-subjects
 * @access Public
 */
export const getAllExamSubjects = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.exam && isValidObjectId(queryString.exam)) {
    matchStage['exam'] = new mongoose.Types.ObjectId(queryString.exam as string);
    delete queryString.exam;
  }

  const baseAggregation = [{ $match: matchStage }, ...examSubjectAggregation];

  const queryManager = new AggregateQueryManager({
    model: ExamSubject,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [examSubjects, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam subjects fetched successfully',
    data: { examSubjects, pagination },
  });
};

/**
 * Get exam subjects by exam
 * @route GET /api/v1/education/exam-subjects/exam/:examId
 * @access Public
 */
export const getExamSubjectsByExam = async (req: Request, res: Response): Promise<void> => {
  const { examId } = req.params;
  const queryString = req.query;

  if (!isValidObjectId(examId)) {
    throw new BadRequestError('Please provide a valid exam ID');
  }

  const examExists = await Exam.findById(examId);
  if (!examExists) {
    throw new NotFoundError(`No exam found with id: ${examId}`);
  }

  const matchStage = { exam: new mongoose.Types.ObjectId(examId) };
  const baseAggregation = [{ $match: matchStage }, ...examSubjectAggregation];

  const queryManager = new AggregateQueryManager({
    model: ExamSubject,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [examSubjects, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam subjects fetched successfully',
    data: { examSubjects, pagination },
  });
};

/**
 * Get a specific exam subject by ID
 * @route GET /api/v1/education/exam-subjects/:id
 * @access Public
 */
export const getExamSubjectById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam subject ID');
  }

  const [examSubject] = await ExamSubject.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...examSubjectAggregation]);

  if (!examSubject) {
    throw new NotFoundError(`No exam subject found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam subject fetched successfully',
    data: { examSubject },
  });
};

/**
 * Update an exam subject
 * @route PATCH /api/v1/education/exam-subjects/:id
 * @access Admin only
 */
export const updateExamSubject = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateExamSubjectInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam subject ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const examSubject = await ExamSubject.findById(id);
  if (!examSubject) {
    throw new NotFoundError(`No exam subject found with id: ${id}`);
  }

  // Validate exam if provided
  if (updateData.exam && !isValidObjectId(updateData.exam)) {
    throw new BadRequestError('Please provide a valid exam ID');
  }

  if (updateData.exam) {
    const examExists = await Exam.findById(updateData.exam);
    if (!examExists) {
      throw new NotFoundError(`No exam found with id: ${updateData.exam}`);
    }
  }

  // Validate the update data
  const validatedData = await validateData(createExamSubjectSchema.partial(), updateData);

  // Check for duplicate name within the same exam
  if (validatedData.name || validatedData.exam) {
    const examId = validatedData.exam || examSubject.exam;
    const examSubjectName = validatedData.name || examSubject.name;

    const existingExamSubject = await ExamSubject.findOne({
      name: examSubjectName,
      exam: examId,
      _id: { $ne: id },
    });

    if (existingExamSubject) {
      throw new BadRequestError(`An exam subject with the name '${examSubjectName}' already exists for this exam`);
    }
  }

  await ExamSubject.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated exam subject with aggregation
  const [updatedExamSubject] = await ExamSubject.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...examSubjectAggregation]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam subject updated successfully',
    data: { examSubject: updatedExamSubject },
  });
};

/**
 * Delete an exam subject
 * @route DELETE /api/v1/education/exam-subjects/:id
 * @access Super Admin only
 */
export const deleteExamSubject = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam subject ID');
  }

  const examSubject = await ExamSubject.findById(id);
  if (!examSubject) {
    throw new NotFoundError(`No exam subject found with id: ${id}`);
  }

  // TODO: Check if there are any references to this exam subject in other collections
  // If yes, prevent deletion or implement cascade delete based on requirements

  await ExamSubject.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam subject deleted successfully',
    data: null,
  });
};
