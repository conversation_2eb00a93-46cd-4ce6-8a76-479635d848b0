import { createOptionsKeyMap } from '@/validation/utils/form.utils';

export const tuitionTypeMap = {
  private: { key: 'private', label: 'Private Tuition' },
  school: { key: 'school', label: 'School' },
  college: { key: 'college', label: 'College' },
  institute: { key: 'institute', label: 'Institute' },
  other: { key: 'other', label: 'Other' },
} as const;

export const tuitionTypeOptions = createOptionsKeyMap(tuitionTypeMap);
export type ITuitionTypeMap = keyof typeof tuitionTypeMap;

export const currencyMap = {
  inr: { key: 'inr', label: 'INR', symbol: '₹' },
  usd: { key: 'usd', label: 'USD', symbol: '$' },
} as const;

export const currencyOptions = createOptionsKeyMap(currencyMap);
export type ICurrencyMap = keyof typeof currencyMap;

export const rateTypeMap = {
  perHour: { key: 'perHour', label: 'Per Hour' },
  perMonth: { key: 'perMonth', label: 'Per Month' },
} as const;

export const rateTypeOptions = createOptionsKeyMap(rateTypeMap);
export type IRateTypeMap = keyof typeof rateTypeMap;
