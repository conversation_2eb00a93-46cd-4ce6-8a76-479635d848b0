import dotenv from 'dotenv';
import app from './app';
import dbConnect from '@/config/database';
import { getOriginConfig } from '@/utils/config.utils';

dotenv.config();
const originConfig = getOriginConfig();

const startServer = async (): Promise<void> => {
  try {
    const PORT: number = Number(process.env.PORT || 3035);
    await dbConnect(process.env.MONGO_URI as string);

    app.listen(PORT, () => {
      const origin = originConfig.origin;

      origin.includes('localhost')
        ? console.log(`Server running at ${origin} in ${originConfig.environment} mode...`)
        : console.log(`Server running at ${origin} at port ${PORT} in ${originConfig.environment} mode...`);
    });
  } catch (err) {
    console.error('Error starting server:', (err as Error).message);
    process.exit(1);
  }
};

startServer();
