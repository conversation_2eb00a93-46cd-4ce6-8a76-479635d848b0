import { config } from '@/config';

interface OriginConfig {
  origin: string;
  environment: string;
  corsOrigins: string[];
}

export const getOriginConfig = (): OriginConfig => {
  const isProduction = process.env.NODE_ENV === 'production';
  const { DEVELOPMENT_ORIGIN, PRODUCTION_ORIGIN } = config;

  if (!DEVELOPMENT_ORIGIN || !PRODUCTION_ORIGIN) {
    throw new Error('Missing required environment variables: DEVELOPMENT_ORIGIN or PRODUCTION_ORIGIN');
  }

  // In development, allow both the frontend and backend origins
  const developmentCorsOrigins = ['http://localhost:3000', 'http://localhost:3040', DEVELOPMENT_ORIGIN];
  // In production, you might want to be more restrictive
  const productionCorsOrigins = [PRODUCTION_ORIGIN];

  return {
    origin: isProduction ? PRODUCTION_ORIGIN : DEVELOPMENT_ORIGIN,
    environment: isProduction ? 'production' : 'development',
    corsOrigins: isProduction ? productionCorsOrigins : developmentCorsOrigins,
  };
};
