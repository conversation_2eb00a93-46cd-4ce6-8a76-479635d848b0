import { businesLocationTypesMap } from '@/validation/schemas/other/index.maps';
import { CreateBusinessLocationInput } from '@/validation/schemas/other/index.schema';
import mongoose, { Document, Schema } from 'mongoose';

export interface IBusinessLocation extends CreateBusinessLocationInput {}

export interface BusinessLocationDocument extends IBusinessLocation, Document {
  createdAt: Date;
  updatedAt: Date;
}

const businessLocationSchema = new Schema<BusinessLocationDocument>(
  {
    name: {
      type: String,
      required: [true, 'Business name is required'],
      trim: true,
    },
    location: {
      type: String,
      required: [true, 'Location is required'],
      trim: true,
    },
    type: {
      type: String,
      enum: Object.keys(businesLocationTypesMap),
      default: 'other',
    },
    coordinates: { type: { lat: Number, lng: Number }, _id: false },
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Add indexes to optimize queries
businessLocationSchema.index({ name: 1 });
businessLocationSchema.index({ location: 1 });
businessLocationSchema.index({ isActive: 1 });

// Add compound index for name and location to ensure uniqueness
businessLocationSchema.index({ name: 1, location: 1 }, { unique: true });

const BusinessLocation = mongoose.models.BusinessLocation || mongoose.model<BusinessLocationDocument>('BusinessLocation', businessLocationSchema);

export default BusinessLocation;
