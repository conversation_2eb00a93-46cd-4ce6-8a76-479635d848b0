import crypto from 'crypto';

interface RandomStringOptions {
  length?: number;
  encoding?: BufferEncoding;
}

interface HashOptions {
  algorithm?: string;
  encoding?: crypto.BinaryToTextEncoding;
}

export const generateRandomString = (options?: RandomStringOptions): string => {
  const length = options?.length || 32;
  const encoding = options?.encoding || 'hex';

  return crypto.randomBytes(length).toString(encoding);
};

export const hashString = (input: string, options?: HashOptions): string => {
  const algorithm = options?.algorithm || 'sha256';
  const encoding = options?.encoding || 'hex';

  return crypto.createHash(algorithm).update(input).digest(encoding);
};

export const generateSessionId = (): string => {
  return generateRandomString({ length: 30 });
};

export const generateVerificationToken = (): string => {
  return generateRandomString({ length: 35 });
};

export const generateResetToken = (): string => {
  return generateRandomString({ length: 40 });
};
