import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import CourseType from '@/models/course/course-type.model';
import Course from '@/models/course/course.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createCourseTypeSchema, UpdateCourseTypeInput } from '@/validation/schemas/education/course.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const courseTypeAggregation: PipelineStage[] = [{ $sort: { name: 1 } }];

/**
 * Create a new course type
 * @route POST /api/v1/education/course-types
 * @access Admin only
 */
export const createCourseType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createCourseTypeSchema, req.body);
  const { name } = validatedData;

  const existingCourseType = await CourseType.findOne({ name });
  if (existingCourseType) {
    throw new BadRequestError(`A course type with the name '${name}' already exists`);
  }

  const courseType = await CourseType.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Course type created successfully',
    data: { courseType },
  });
};

/**
 * Get all course types
 * @route GET /api/v1/education/course-types
 * @access Public
 */
export const getAllCourseTypes = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const baseAggregation = [...courseTypeAggregation];

  const queryManager = new AggregateQueryManager({
    model: CourseType,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [courseTypes, pagination] = await Promise.all([
    queryManager.execute(),
    queryManager.getPaginationMetadata(),
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Course types fetched successfully',
    data: { courseTypes, pagination },
  });
};

/**
 * Get a specific course type by ID
 * @route GET /api/v1/education/course-types/:id
 * @access Public
 */
export const getCourseTypeById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid course type ID');
  }

  const [courseType] = await CourseType.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...courseTypeAggregation,
  ]);

  if (!courseType) {
    throw new NotFoundError(`No course type found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Course type fetched successfully',
    data: { courseType },
  });
};

/**
 * Update a course type
 * @route PATCH /api/v1/education/course-types/:id
 * @access Admin only
 */
export const updateCourseType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateCourseTypeInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid course type ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const courseType = await CourseType.findById(id);
  if (!courseType) {
    throw new NotFoundError(`No course type found with id: ${id}`);
  }

  // Validate the update data
  const validatedData = await validateData(createCourseTypeSchema.partial(), updateData);

  // Check for duplicate name
  if (validatedData.name) {
    const existingCourseType = await CourseType.findOne({ name: validatedData.name, _id: { $ne: id } });
    if (existingCourseType) {
      throw new BadRequestError(`A course type with the name '${validatedData.name}' already exists`);
    }
  }

  await CourseType.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated course type with aggregation
  const [updatedCourseType] = await CourseType.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...courseTypeAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Course type updated successfully',
    data: { courseType: updatedCourseType },
  });
};

/**
 * Delete a course type
 * @route DELETE /api/v1/education/course-types/:id
 * @access Super Admin only
 */
export const deleteCourseType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid course type ID');
  }

  const courseType = await CourseType.findById(id);
  if (!courseType) {
    throw new NotFoundError(`No course type found with id: ${id}`);
  }

  // Check if there are any courses associated with this course type
  const associatedCourses = await Course.findOne({ courseType: id });
  if (associatedCourses) {
    throw new BadRequestError(
      'Cannot delete this course type because there are courses associated with it. Please delete those courses first.'
    );
  }

  await CourseType.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Course type deleted successfully',
    data: null,
  });
};
