import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from '@/constants';
import { ICustomAPIError, CustomAPIError } from '@/errors';
import { ZodError } from 'zod';

type ErrorTypes = Error | ICustomAPIError | ValidationError | DuplicateKeyError | CastError | ZodError;

interface ValidationError extends Error {
  errors: Record<string, { message: string; path?: string }>;
}

interface DuplicateKeyError extends Error {
  code: number;
  keyValue: Record<string, any>;
}

interface CastError extends Error {
  value: string;
}

interface CustomErrorResponse {
  statusCode: number;
  message: string;
  errors: Record<string, string>;
}

const errorHandlerMiddleware = (err: ErrorTypes, _req: Request, res: Response, _next: NextFunction): void => {
  const customError: CustomErrorResponse = {
    statusCode: err instanceof CustomAPIError ? err.statusCode : StatusCodes.INTERNAL_SERVER_ERROR,
    message: err.message || 'Something went wrong',
    errors: {},
  };

  switch (true) {
    case err instanceof ZodError:
      handleZodError(err as ZodError, customError);
      break;
    case 'errors' in err && err.name === 'ValidationError':
      handleValidationError(err as ValidationError, customError);
      break;
    case 'code' in err && err.code === 11000 && 'keyValue' in err:
      handleDuplicateKeyError(err as DuplicateKeyError, customError);
      break;
    case err.name === 'CastError' && 'value' in err:
      handleCastError(err as CastError, customError);
      break;
  }

  const status = customError.statusCode.toString().startsWith('4') ? 'fail' : 'error';
  const response: any = { success: false, status, message: customError.message };

  if (Object.keys(customError.errors).length > 0) {
    const errorKey = Object.keys(customError.errors)[0];
    response.message = `${errorKey}: ${customError.errors[errorKey]}`;
  }

  console.error('Error:', customError);

  res.status(customError.statusCode).json(response);
};

const handleZodError = (err: ZodError, customError: CustomErrorResponse): void => {
  customError.statusCode = StatusCodes.BAD_REQUEST;
  customError.message = 'Validation failed';

  err.errors.forEach((error) => {
    customError.errors[error.path.join('.')] = error.message;
  });
};

const handleValidationError = (err: ValidationError, customError: CustomErrorResponse): void => {
  customError.statusCode = StatusCodes.BAD_REQUEST;
  customError.message = 'Validation failed';
  Object.entries(err.errors).forEach(([key, value]) => {
    customError.errors[key] = value.message;
  });
};

const handleDuplicateKeyError = (err: DuplicateKeyError, customError: CustomErrorResponse): void => {
  customError.statusCode = StatusCodes.BAD_REQUEST;
  customError.message = 'Duplicate value entered';
  Object.entries(err.keyValue).forEach(([key, value]) => {
    customError.errors[key] = `The value '${value}' already exists. Please choose another value.`;
  });
};

const handleCastError = (err: CastError, customError: CustomErrorResponse): void => {
  customError.statusCode = StatusCodes.NOT_FOUND;
  customError.message = 'Resource not found';
  customError.errors['id'] = `No item found with id: ${err.value}`;
};

export default errorHandlerMiddleware;
