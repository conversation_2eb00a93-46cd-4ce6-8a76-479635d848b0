import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import HobbyType from '@/models/hobby/hobby-type.model';
import Hobby from '@/models/hobby/hobby.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createHobbyTypeSchema, UpdateHobbyTypeInput } from '@/validation/schemas/education/hobby.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const hobbyTypeAggregation: PipelineStage[] = [{ $sort: { name: 1 } }];

/**
 * Create a new hobby type
 * @route POST /api/v1/education/hobby-types
 * @access Admin only
 */
export const createHobbyType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createHobbyTypeSchema, req.body);
  const { name } = validatedData;

  const existingHobbyType = await HobbyType.findOne({ name });
  if (existingHobbyType) {
    throw new BadRequestError(`A hobby type with the name '${name}' already exists`);
  }

  const hobbyType = await HobbyType.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Hobby type created successfully',
    data: { hobbyType },
  });
};

/**
 * Get all hobby types
 * @route GET /api/v1/education/hobby-types
 * @access Public
 */
export const getAllHobbyTypes = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const baseAggregation = [...hobbyTypeAggregation];

  const queryManager = new AggregateQueryManager({
    model: HobbyType,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [hobbyTypes, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Hobby types fetched successfully',
    data: { hobbyTypes, pagination },
  });
};

/**
 * Get a specific hobby type by ID
 * @route GET /api/v1/education/hobby-types/:id
 * @access Public
 */
export const getHobbyTypeById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid hobby type ID');
  }

  const [hobbyType] = await HobbyType.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...hobbyTypeAggregation]);

  if (!hobbyType) {
    throw new NotFoundError(`No hobby type found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Hobby type fetched successfully',
    data: { hobbyType },
  });
};

/**
 * Update a hobby type
 * @route PATCH /api/v1/education/hobby-types/:id
 * @access Admin only
 */
export const updateHobbyType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateHobbyTypeInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid hobby type ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const hobbyType = await HobbyType.findById(id);
  if (!hobbyType) {
    throw new NotFoundError(`No hobby type found with id: ${id}`);
  }

  // Validate the update data
  const validatedData = await validateData(createHobbyTypeSchema.partial(), updateData);

  // Check for duplicate name
  if (validatedData.name) {
    const existingHobbyType = await HobbyType.findOne({ name: validatedData.name, _id: { $ne: id } });
    if (existingHobbyType) {
      throw new BadRequestError(`A hobby type with the name '${validatedData.name}' already exists`);
    }
  }

  await HobbyType.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated hobby type with aggregation
  const [updatedHobbyType] = await HobbyType.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...hobbyTypeAggregation]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Hobby type updated successfully',
    data: { hobbyType: updatedHobbyType },
  });
};

/**
 * Delete a hobby type
 * @route DELETE /api/v1/education/hobby-types/:id
 * @access Super Admin only
 */
export const deleteHobbyType = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid hobby type ID');
  }

  const hobbyType = await HobbyType.findById(id);
  if (!hobbyType) {
    throw new NotFoundError(`No hobby type found with id: ${id}`);
  }

  // Check if there are any hobbies associated with this hobby type
  const associatedHobbies = await Hobby.findOne({ hobbyType: id });
  if (associatedHobbies) {
    throw new BadRequestError('Cannot delete this hobby type because there are hobbies associated with it. Please delete those hobbies first.');
  }

  await HobbyType.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Hobby type deleted successfully',
    data: null,
  });
};
