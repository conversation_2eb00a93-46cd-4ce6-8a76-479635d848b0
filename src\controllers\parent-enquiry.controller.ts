import { Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Enquiry from '@/models/enquiry.model';
import { AuthenticatedRequest } from '@/types/express';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { AggregateQueryManager } from '@/utils/aggregate.utils';

export const getParentEnquiries = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const queryString = req.query;

  const isActive = queryString.isActive === 'true' ? true : false;
  delete queryString.isActive;

  // const isConverted = queryString.isConverted === 'true' ? true : false;
  // delete queryString.isConverted;

  const baseAggregation: PipelineStage[] = [
    {
      $match: {
        user: new mongoose.Types.ObjectId(userId),
        isActive,
      },
    },
    {
      $lookup: {
        from: 'childprofiles',
        localField: 'childProfile',
        foreignField: '_id',
        as: 'childProfileDetails',
      },
    },
    { $unwind: '$childProfileDetails' },
    {
      $lookup: {
        from: 'boards',
        localField: 'board',
        foreignField: '_id',
        as: 'boardDetails',
      },
    },
    { $unwind: { path: '$boardDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'classes',
        localField: 'class',
        foreignField: '_id',
        as: 'classDetails',
      },
    },
    { $unwind: { path: '$classDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'subjects',
        localField: 'subjects',
        foreignField: '_id',
        as: 'subjectDetails',
      },
    },
    // College specific lookups
    {
      $lookup: {
        from: 'streams',
        localField: 'stream',
        foreignField: '_id',
        as: 'streamDetails',
      },
    },
    { $unwind: { path: '$streamDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'degreelevels',
        localField: 'degreeLevel',
        foreignField: '_id',
        as: 'degreeLevelDetails',
      },
    },
    { $unwind: { path: '$degreeLevelDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'degrees',
        localField: 'degree',
        foreignField: '_id',
        as: 'degreeDetails',
      },
    },
    { $unwind: { path: '$degreeDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'branches',
        localField: 'branch',
        foreignField: '_id',
        as: 'branchDetails',
      },
    },
    { $unwind: { path: '$branchDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'collegesubjects',
        localField: 'collegeSubjects',
        foreignField: '_id',
        as: 'collegeSubjectDetails',
      },
    },
    // Hobby specific lookups
    {
      $lookup: {
        from: 'hobbytypes',
        localField: 'hobbyType',
        foreignField: '_id',
        as: 'hobbyTypeDetails',
      },
    },
    { $unwind: { path: '$hobbyTypeDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'hobbies',
        localField: 'hobby',
        foreignField: '_id',
        as: 'hobbyDetails',
      },
    },
    { $unwind: { path: '$hobbyDetails', preserveNullAndEmptyArrays: true } },
    // Language specific lookups
    {
      $lookup: {
        from: 'languagetypes',
        localField: 'languageType',
        foreignField: '_id',
        as: 'languageTypeDetails',
      },
    },
    { $unwind: { path: '$languageTypeDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'languages',
        localField: 'language',
        foreignField: '_id',
        as: 'languageDetails',
      },
    },
    { $unwind: { path: '$languageDetails', preserveNullAndEmptyArrays: true } },
    // Course specific lookups
    {
      $lookup: {
        from: 'coursetypes',
        localField: 'courseType',
        foreignField: '_id',
        as: 'courseTypeDetails',
      },
    },
    { $unwind: { path: '$courseTypeDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'courses',
        localField: 'course',
        foreignField: '_id',
        as: 'courseDetails',
      },
    },
    { $unwind: { path: '$courseDetails', preserveNullAndEmptyArrays: true } },
    // Exam specific lookups
    {
      $lookup: {
        from: 'examcategories',
        localField: 'examCategory',
        foreignField: '_id',
        as: 'examCategoryDetails',
      },
    },
    { $unwind: { path: '$examCategoryDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'exams',
        localField: 'exam',
        foreignField: '_id',
        as: 'examDetails',
      },
    },
    { $unwind: { path: '$examDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'examsubjects',
        localField: 'examSubjects',
        foreignField: '_id',
        as: 'examSubjectDetails',
      },
    },
  ];

  const queryManager = new AggregateQueryManager({ model: Enquiry, queryString, baseAggregation }).filter().sort().paginate();

  const [enquiries, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Enquiries fetched successfully',
    data: { enquiries, pagination },
  });
};

export const getParentEnquiryById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid enquiry ID');
  }

  const [enquiry] = await Enquiry.aggregate([
    {
      $match: {
        _id: new mongoose.Types.ObjectId(id),
        user: new mongoose.Types.ObjectId(userId),
      },
    },
    {
      $lookup: {
        from: 'childprofiles',
        localField: 'childProfile',
        foreignField: '_id',
        as: 'childProfileDetails',
      },
    },
    { $unwind: '$childProfileDetails' },
    // School specific lookups
    {
      $lookup: {
        from: 'boards',
        localField: 'board',
        foreignField: '_id',
        as: 'boardDetails',
      },
    },
    {
      $lookup: {
        from: 'classes',
        localField: 'class',
        foreignField: '_id',
        as: 'classDetails',
      },
    },
    {
      $lookup: {
        from: 'subjects',
        localField: 'subjects',
        foreignField: '_id',
        as: 'subjectDetails',
      },
    },
    // College specific lookups
    {
      $lookup: {
        from: 'streams',
        localField: 'stream',
        foreignField: '_id',
        as: 'streamDetails',
      },
    },
    {
      $lookup: {
        from: 'degreelevels',
        localField: 'degreeLevel',
        foreignField: '_id',
        as: 'degreeLevelDetails',
      },
    },
    {
      $lookup: {
        from: 'degrees',
        localField: 'degree',
        foreignField: '_id',
        as: 'degreeDetails',
      },
    },
    {
      $lookup: {
        from: 'branches',
        localField: 'branch',
        foreignField: '_id',
        as: 'branchDetails',
      },
    },
    // Hobby specific lookups
    {
      $lookup: {
        from: 'hobbytypes',
        localField: 'hobbyType',
        foreignField: '_id',
        as: 'hobbyTypeDetails',
      },
    },
    {
      $lookup: {
        from: 'hobbies',
        localField: 'hobby',
        foreignField: '_id',
        as: 'hobbyDetails',
      },
    },
    // Language specific lookups
    {
      $lookup: {
        from: 'languagetypes',
        localField: 'languageType',
        foreignField: '_id',
        as: 'languageTypeDetails',
      },
    },
    {
      $lookup: {
        from: 'languages',
        localField: 'language',
        foreignField: '_id',
        as: 'languageDetails',
      },
    },
    // Course specific lookups
    {
      $lookup: {
        from: 'coursetypes',
        localField: 'courseType',
        foreignField: '_id',
        as: 'courseTypeDetails',
      },
    },
    {
      $lookup: {
        from: 'courses',
        localField: 'course',
        foreignField: '_id',
        as: 'courseDetails',
      },
    },
    // Exam specific lookups
    {
      $lookup: {
        from: 'examcategories',
        localField: 'examCategory',
        foreignField: '_id',
        as: 'examCategoryDetails',
      },
    },
    {
      $lookup: {
        from: 'exams',
        localField: 'exam',
        foreignField: '_id',
        as: 'examDetails',
      },
    },
    {
      $lookup: {
        from: 'examsubjects',
        localField: 'examSubjects',
        foreignField: '_id',
        as: 'examSubjectDetails',
      },
    },
  ]);

  if (!enquiry) {
    throw new NotFoundError(`No enquiry found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Enquiry fetched successfully',
    data: { enquiry },
  });
};

export const updateEnquiryStatus = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const { id } = req.params;
  const { isActive } = req.body;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid enquiry ID');
  }

  if (typeof isActive !== 'boolean') {
    throw new BadRequestError('Please provide a valid status');
  }

  const [updatedEnquiry] = await Enquiry.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id), user: new mongoose.Types.ObjectId(userId) } },
    { $set: { isActive: isActive } },
    { $lookup: { from: 'childprofiles', localField: 'childProfile', foreignField: '_id', as: 'childProfileDetails' } },
    { $unwind: '$childProfileDetails' },
    // School specific lookups
    {
      $lookup: {
        from: 'boards',
        localField: 'board',
        foreignField: '_id',
        as: 'boardDetails',
      },
    },
    {
      $lookup: {
        from: 'classes',
        localField: 'class',
        foreignField: '_id',
        as: 'classDetails',
      },
    },
    {
      $lookup: {
        from: 'subjects',
        localField: 'subjects',
        foreignField: '_id',
        as: 'subjectDetails',
      },
    },
    // College specific lookups
    {
      $lookup: {
        from: 'streams',
        localField: 'stream',
        foreignField: '_id',
        as: 'streamDetails',
      },
    },
    {
      $lookup: {
        from: 'degreelevels',
        localField: 'degreeLevel',
        foreignField: '_id',
        as: 'degreeLevelDetails',
      },
    },
    {
      $lookup: {
        from: 'degrees',
        localField: 'degree',
        foreignField: '_id',
        as: 'degreeDetails',
      },
    },
    {
      $lookup: {
        from: 'branches',
        localField: 'branch',
        foreignField: '_id',
        as: 'branchDetails',
      },
    },
    // Hobby specific lookups
    {
      $lookup: {
        from: 'hobbytypes',
        localField: 'hobbyType',
        foreignField: '_id',
        as: 'hobbyTypeDetails',
      },
    },
    {
      $lookup: {
        from: 'hobbies',
        localField: 'hobby',
        foreignField: '_id',
        as: 'hobbyDetails',
      },
    },
    // Language specific lookups
    {
      $lookup: {
        from: 'languagetypes',
        localField: 'languageType',
        foreignField: '_id',
        as: 'languageTypeDetails',
      },
    },
    {
      $lookup: {
        from: 'languages',
        localField: 'language',
        foreignField: '_id',
        as: 'languageDetails',
      },
    },
    // Course specific lookups
    {
      $lookup: {
        from: 'coursetypes',
        localField: 'courseType',
        foreignField: '_id',
        as: 'courseTypeDetails',
      },
    },
    {
      $lookup: {
        from: 'courses',
        localField: 'course',
        foreignField: '_id',
        as: 'courseDetails',
      },
    },
    // Exam specific lookups
    {
      $lookup: {
        from: 'examcategories',
        localField: 'examCategory',
        foreignField: '_id',
        as: 'examCategoryDetails',
      },
    },
    {
      $lookup: {
        from: 'exams',
        localField: 'exam',
        foreignField: '_id',
        as: 'examDetails',
      },
    },
    {
      $lookup: {
        from: 'examsubjects',
        localField: 'examSubjects',
        foreignField: '_id',
        as: 'examSubjectDetails',
      },
    },
  ]);

  if (!updatedEnquiry) {
    throw new NotFoundError(`No enquiry found with id: ${id}`);
  }

  await Enquiry.updateOne({ _id: id }, { $set: { isActive: isActive } });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Enquiry status updated successfully',
    data: { enquiry: updatedEnquiry },
  });
};
