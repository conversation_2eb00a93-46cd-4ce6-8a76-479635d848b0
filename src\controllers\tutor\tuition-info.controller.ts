import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import { validateData } from '@/utils';
import { AuthenticatedRequest } from '@/types/express';
import { isValidObjectId } from 'mongoose';

// Models
import TuitionInfo from '@/models/tutor/tuition-info.model';

// Schemas
import { createTuitionInfoSchema, updateTuitionInfoSchema } from '@/validation/schemas/tutor/profiles/tuition-info.schema';

export const getTuitionInfo = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;

  const tuitionInfo = await TuitionInfo.findOne({ userId });

  if (!tuitionInfo) throw new NotFoundError('Tuition info not found');

  res.status(StatusCodes.OK).json({ success: true, message: 'Tuition info fetched successfully', data: { tuitionInfo } });
};

export const createTuitionInfo = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createTuitionInfoSchema, req.body);
  const userId = req.user?.userId;

  const existingTuitionInfo = await TuitionInfo.findOne({ userId });
  if (existingTuitionInfo) throw new BadRequestError('Tuition info already exists for this user. Use update instead.');

  const tuitionInfo = await TuitionInfo.create({ ...validatedData, userId });
  res.status(StatusCodes.CREATED).json({ success: true, message: 'Tuition info created successfully', data: { tuitionInfo } });
};

export const updateTuitionInfo = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const validatedData = await validateData(updateTuitionInfoSchema, req.body);
  const userId = req.user?.userId;

  if (!isValidObjectId(id)) throw new BadRequestError('Invalid tuition info ID');

  const tuitionInfo = await TuitionInfo.findOneAndUpdate({ _id: id, userId }, validatedData, { new: true, runValidators: true });

  if (!tuitionInfo) throw new NotFoundError('Tuition info not found');

  res.status(StatusCodes.OK).json({ success: true, message: 'Tuition info updated successfully', data: { tuitionInfo } });
};

export const deleteTuitionInfo = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const userId = req.user?.userId;

  if (!isValidObjectId(id)) throw new BadRequestError('Invalid tuition info ID');

  const tuitionInfo = await TuitionInfo.findOneAndDelete({ _id: id, userId });

  if (!tuitionInfo) throw new NotFoundError('Tuition info not found');

  res.status(StatusCodes.OK).json({ success: true, message: 'Tuition info deleted successfully', data: { tuitionInfo } });
};
