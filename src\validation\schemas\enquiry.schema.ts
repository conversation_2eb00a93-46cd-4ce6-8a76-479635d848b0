import { z } from 'zod';
import { objectIdSchema } from './common.schema';
import { tutorGenderMap, ITutorGenderMap, startTimeMap, IStartTimeMap, deliveryModeMap, IDeliveryModeMap } from './enquiry.maps';

// Search schema
export const searchQuerySchema = z.object({
  query: z.string().min(3, 'Search query must be at least 3 characters long'),
});

// Location schema
const locationSchema = z.object({
  address: z.string().min(1, 'Address is required'),
  landmark: z.string().optional(),
  coordinates: z
    .object({
      lat: z.number(),
      lng: z.number(),
    })
    .optional(),
});

// Preferences schema
export const tutoringPreferencesSchema = z.object({
  sessionId: z.string().optional(),
  location: locationSchema,
  tutorGender: z.enum(Object.keys(tutorGenderMap) as [ITutorGenderMap, ...ITutorGenderMap[]]).default('any'),
  classesPerWeek: z.number().min(1).max(7),
  startTime: z.enum(Object.keys(startTimeMap) as [IStartTimeMap, ...IStartTimeMap[]]),
  deliveryModes: z
    .array(z.enum(Object.keys(deliveryModeMap) as [IDeliveryModeMap, ...IDeliveryModeMap[]]))
    .min(1, 'At least one delivery mode is required'),
  specialRequirements: z.string().optional(),
});

// School enquiry schema
const schoolEnquirySchema = z.object({
  category: z.literal('schools'),
  board: objectIdSchema,
  class: objectIdSchema,
  subjects: z.array(objectIdSchema).min(1, 'At least one subject is required'),
  allSubjects: z.boolean().optional(),
});

// College enquiry schema
const collegeEnquirySchema = z.object({
  category: z.literal('colleges'),
  stream: objectIdSchema,
  degreeLevel: objectIdSchema,
  degree: objectIdSchema,
  branch: objectIdSchema.optional(),
  collegeSubjects: z.array(objectIdSchema).optional(),
  allSubjects: z.boolean().optional(),
});

// Hobby enquiry schema
const hobbyEnquirySchema = z.object({
  category: z.literal('hobbies'),
  hobbyType: objectIdSchema,
  hobby: objectIdSchema,
});

// Language enquiry schema
const languageEnquirySchema = z.object({
  category: z.literal('languages'),
  languageType: objectIdSchema,
  language: objectIdSchema,
});

// Course enquiry schema
const courseEnquirySchema = z.object({
  category: z.literal('it_courses'),
  courseType: objectIdSchema,
  course: objectIdSchema,
});

// Exam enquiry schema
const competitiveExamEnquirySchema = z.object({
  category: z.literal('exams'),
  examCategory: objectIdSchema,
  exam: objectIdSchema,
  examSubjects: z.array(objectIdSchema).optional(),
  allSubjects: z.boolean().optional(),
});

// Combined category schema using discriminated union
const categorySchema = z.discriminatedUnion('category', [
  schoolEnquirySchema,
  collegeEnquirySchema,
  hobbyEnquirySchema,
  languageEnquirySchema,
  courseEnquirySchema,
  competitiveExamEnquirySchema,
]);

// Create enquiry schema
export const createEnquirySchema = z
  .object({
    childProfileId: objectIdSchema,
    preferences: tutoringPreferencesSchema,
    isStudentSpecial: z.boolean().optional().default(false),
  })
  .and(categorySchema);

// Types
export type SearchQueryInput = z.infer<typeof searchQuerySchema>;
export type TutoringPreferencesInput = z.infer<typeof tutoringPreferencesSchema>;
export type CreateEnquiryInput = z.infer<typeof createEnquirySchema>;
