import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import DegreeLevel from '@/models/college/degree-level.model';
import Stream from '@/models/college/stream.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createDegreeLevelSchema, UpdateDegreeLevelInput } from '@/validation/schemas/education/college.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const degreeLevelAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'streams',
      localField: 'stream',
      foreignField: '_id',
      as: 'streamDetails',
    },
  },
  {
    $addFields: {
      streamDetails: { $arrayElemAt: ['$streamDetails', 0] },
    },
  },
  { $sort: { name: 1 } },
];

/**
 * Create a new degree level
 * @route POST /api/v1/education/degree-levels
 * @access Private (Admin)
 */
export const createDegreeLevel = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createDegreeLevelSchema, req.body);
  const { name, stream } = validatedData;

  if (!isValidObjectId(stream)) {
    throw new BadRequestError('Please provide a valid stream ID');
  }

  const streamExists = await Stream.findById(stream);
  if (!streamExists) {
    throw new NotFoundError(`No stream found with id: ${stream}`);
  }

  const existingDegreeLevel = await DegreeLevel.findOne({ name, stream });
  if (existingDegreeLevel) {
    throw new BadRequestError(`A degree level with the name '${name}' already exists for this stream`);
  }

  const degreeLevel = await DegreeLevel.create({
    ...validatedData,
  });

  const [populatedDegreeLevel] = await DegreeLevel.aggregate([{ $match: { _id: degreeLevel._id } }, ...degreeLevelAggregation]);

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Degree level created successfully',
    data: { degreeLevel: populatedDegreeLevel },
  });
};

/**
 * Get all degree levels
 * @route GET /api/v1/education/degree-levels
 * @access Public
 */
export const getAllDegreeLevels = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.stream && isValidObjectId(queryString.stream)) {
    matchStage['stream'] = new mongoose.Types.ObjectId(queryString.stream as string);
    delete queryString.stream;
  }

  const baseAggregation = [{ $match: matchStage }, ...degreeLevelAggregation];

  const queryManager = new AggregateQueryManager({
    model: DegreeLevel,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [degreeLevels, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Degree levels fetched successfully',
    data: { degreeLevels, pagination },
  });
};

/**
 * Get a degree level by ID
 * @route GET /api/v1/education/degree-levels/:id
 * @access Public
 */
export const getDegreeLevelById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid degree level ID');
  }

  const [degreeLevel] = await DegreeLevel.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...degreeLevelAggregation]);

  if (!degreeLevel) {
    throw new NotFoundError(`No degree level found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Degree level fetched successfully',
    data: { degreeLevel },
  });
};

/**
 * Update a degree level
 * @route PATCH /api/v1/education/degree-levels/:id
 * @access Private (Admin)
 */
export const updateDegreeLevel = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateDegreeLevelInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid degree level ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const degreeLevel = await DegreeLevel.findById(id);
  if (!degreeLevel) {
    throw new NotFoundError(`No degree level found with id: ${id}`);
  }

  // Validate stream if provided
  if (updateData.stream && !isValidObjectId(updateData.stream)) {
    throw new BadRequestError('Please provide a valid stream ID');
  }

  if (updateData.stream) {
    const streamExists = await Stream.findById(updateData.stream);
    if (!streamExists) {
      throw new NotFoundError(`No stream found with id: ${updateData.stream}`);
    }
  }

  // Check for duplicate if name or stream is being updated
  if (updateData.name || updateData.stream) {
    const name = updateData.name || degreeLevel.name;
    const stream = updateData.stream || degreeLevel.stream;

    const existingDegreeLevel = await DegreeLevel.findOne({
      name,
      stream,
      _id: { $ne: id },
    });

    if (existingDegreeLevel) {
      throw new BadRequestError(`A degree level with the name '${name}' already exists for this stream`);
    }
  }

  await DegreeLevel.findByIdAndUpdate(id, updateData, { runValidators: true });

  const [updatedDegreeLevel] = await DegreeLevel.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...degreeLevelAggregation]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Degree level updated successfully',
    data: { degreeLevel: updatedDegreeLevel },
  });
};

/**
 * Delete a degree level
 * @route DELETE /api/v1/education/degree-levels/:id
 * @access Private (Super Admin)
 */
export const deleteDegreeLevel = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid degree level ID');
  }

  const degreeLevel = await DegreeLevel.findById(id);
  if (!degreeLevel) {
    throw new NotFoundError(`No degree level found with id: ${id}`);
  }

  // Check if there are any degrees associated with this degree level
  const Degree = mongoose.model('Degree');
  const degreesCount = await Degree.countDocuments({ degreeLevel: id });

  if (degreesCount > 0) {
    throw new BadRequestError(`Cannot delete this degree level as it is associated with ${degreesCount} degrees. Please delete those degrees first.`);
  }

  await DegreeLevel.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Degree level deleted successfully',
    data: null,
  });
};
