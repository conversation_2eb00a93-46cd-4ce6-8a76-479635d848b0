# School Service Category Documentation

## Overview

The School service category represents one of the seven main educational domains in the platform. It follows a hierarchical structure that allows for organizing educational content in a structured manner.

## Hierarchy

The School service category follows a three-level hierarchy:

1. **Board** - The educational board or curriculum (e.g., CBSE, ICSE)
2. **Class** - The grade or standard level (e.g., Class I, Class X)
3. **Subject** - The academic subject taught at a specific board and class level (e.g., Mathematics, Science)

```
School
├── Board (CBSE, ICSE, IB, State Board, DEV)
│   ├── Class (Nursery-KG, Class I, Class II, ..., Class XII)
│   │   ├── Subject (Mathematics, Science, English, etc.)
```

## Data Models

### Board Model

The Board model represents educational boards or curriculum systems.

```typescript
// src/models/school/board.model.ts
export interface IBoard {
  name: ISchoolBoardMap; // Enum: 'cbse', 'icse', 'ib', 'state', 'dev'
  isActive: boolean;
}

// Schema definition
const boardSchema = new Schema<BoardDocument>(
  {
    name: {
      type: String,
      enum: Object.keys(schoolBoardMap),
      required: [true, 'Board name is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Index
boardSchema.index({ name: 1 });
```

### Class Model

The Class model represents grade levels within a specific board.

```typescript
// src/models/school/class.model.ts
export interface IClass {
  name: string; // e.g., "Class I", "Class X"
  displayOrder: number; // For sorting classes in correct order
  board: mongoose.Types.ObjectId; // Reference to Board
  isActive: boolean;
}

// Schema definition
const classSchema = new Schema<ClassDocument>(
  {
    name: {
      type: String,
      required: [true, 'Class name is required'],
      trim: true,
    },
    displayOrder: {
      type: Number,
      required: [true, 'Display order is required'],
    },
    board: {
      type: Schema.Types.ObjectId,
      ref: 'Board',
      required: [true, 'Board is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Indexes
classSchema.index({ board: 1, name: 1 }, { unique: true });
classSchema.index({ board: 1, displayOrder: 1 });
```

### Subject Model

The Subject model represents academic subjects taught at a specific board and class level.

```typescript
// src/models/school/subject.model.ts
export interface ISubject {
  name: string;
  board: mongoose.Types.ObjectId; // Reference to Board
  class: mongoose.Types.ObjectId; // Reference to Class
  isActive: boolean;
}

// Schema definition
const subjectSchema = new Schema<SubjectDocument>(
  {
    name: {
      type: String,
      required: [true, 'Subject name is required'],
      trim: true,
    },
    board: {
      type: Schema.Types.ObjectId,
      ref: 'Board',
      required: [true, 'Board is required'],
    },
    class: {
      type: Schema.Types.ObjectId,
      ref: 'Class',
      required: [true, 'Class is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

// Index
subjectSchema.index({ board: 1, class: 1, name: 1 }, { unique: true });
```

## API Endpoints

### Board Endpoints

| Method | Endpoint                       | Description          | Auth Required     |
| ------ | ------------------------------ | -------------------- | ----------------- |
| GET    | `/api/v1/education/boards`     | Get all boards       | No                |
| GET    | `/api/v1/education/boards/:id` | Get a specific board | No                |
| POST   | `/api/v1/education/boards`     | Create a new board   | Yes (admin)       |
| PATCH  | `/api/v1/education/boards/:id` | Update a board       | Yes (admin)       |
| DELETE | `/api/v1/education/boards/:id` | Delete a board       | Yes (super_admin) |

### Class Endpoints

| Method | Endpoint                                   | Description          | Auth Required     |
| ------ | ------------------------------------------ | -------------------- | ----------------- |
| GET    | `/api/v1/education/classes`                | Get all classes      | No                |
| GET    | `/api/v1/education/classes/board/:boardId` | Get classes by board | No                |
| GET    | `/api/v1/education/classes/:id`            | Get a specific class | No                |
| POST   | `/api/v1/education/classes`                | Create a new class   | Yes (admin)       |
| PATCH  | `/api/v1/education/classes/:id`            | Update a class       | Yes (admin)       |
| DELETE | `/api/v1/education/classes/:id`            | Delete a class       | Yes (super_admin) |

### Subject Endpoints

| Method | Endpoint                                    | Description            | Auth Required     |
| ------ | ------------------------------------------- | ---------------------- | ----------------- |
| GET    | `/api/v1/education/subjects`                | Get all subjects       | No                |
| GET    | `/api/v1/education/subjects/class/:classId` | Get subjects by class  | No                |
| GET    | `/api/v1/education/subjects/:id`            | Get a specific subject | No                |
| POST   | `/api/v1/education/subjects`                | Create a new subject   | Yes (admin)       |
| PATCH  | `/api/v1/education/subjects/:id`            | Update a subject       | Yes (admin)       |
| DELETE | `/api/v1/education/subjects/:id`            | Delete a subject       | Yes (super_admin) |

## Predefined Data

### Boards

The system comes with predefined board types:

- CBSE (Central Board of Secondary Education)
- ICSE (Indian Certificate of Secondary Education)
- IB (International Baccalaureate)
- State Board (Various state education boards)
- DEV (Development/Custom board)

### Classes

For each board, the following classes are predefined:

1. Nursery-KG (Display Order: 1)
2. Class I (Display Order: 2)
3. Class II (Display Order: 3)
4. Class III (Display Order: 4)
5. Class IV (Display Order: 5)
6. Class V (Display Order: 6)
7. Class VI (Display Order: 7)
8. Class VII (Display Order: 8)
9. Class VIII (Display Order: 9)
10. Class IX (Display Order: 10)
11. Class X (Display Order: 11)
12. Class XI (Display Order: 12)
13. Class XII (Display Order: 13)

## Data Flow

1. **Creating a Board**: Admin creates a board (e.g., CBSE)
2. **Creating Classes**: Admin creates classes for the board (e.g., Class I, Class II)
3. **Creating Subjects**: Admin creates subjects for each class (e.g., Mathematics, Science)

## Usage Examples

### Creating a Board

```javascript
// POST /api/v1/education/boards
{
  "name": "cbse"
}
```

### Creating a Class

```javascript
// POST /api/v1/education/classes
{
  "name": "Class X",
  "displayOrder": 11,
  "board": "60d5ec9af682d123e4567890" // Board ID
}
```

### Creating a Subject

```javascript
// POST /api/v1/education/subjects
{
  "name": "Mathematics",
  "board": "60d5ec9af682d123e4567890", // Board ID
  "class": "60d5ecb2f682d123e4567891" // Class ID
}
```

### Getting Subjects for a Class

```
GET /api/v1/education/subjects/class/60d5ecb2f682d123e4567891
```

## Relationships with Other Entities

- **Users**: Students and tutors can be associated with specific boards, classes, and subjects
- **Content**: Educational content (notes, videos, etc.) can be categorized by board, class, and subject
- **Assessments**: Tests and quizzes can be organized by board, class, and subject

## Implementation Notes

- The hierarchy is enforced through validation in the controllers
- When creating a subject, both board and class must exist and be related
- Classes have a display order to ensure they appear in the correct sequence
- All entities have an `isActive` flag to enable/disable without deletion
- Referential integrity is enforced when deleting entities:
  - A board cannot be deleted if it has associated classes or subjects
  - A class cannot be deleted if it has associated subjects
  - Subjects can be deleted freely as they are leaf nodes in the hierarchy
