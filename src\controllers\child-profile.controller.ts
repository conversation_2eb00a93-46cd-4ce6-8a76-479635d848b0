import { Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import ChildProfile from '@/models/child-profile.model';
import { validateData } from '@/utils';
import { createChildProfileSchema, UpdateChildProfileInput } from '@/validation/schemas/parent/child-profile.schema';
import { AuthenticatedRequest } from '@/types/express';
import mongoose, { isValidObjectId } from 'mongoose';
import { createFileUploader } from '@/utils/upload.utils';
import { MAX_FILE_SIZE, ACCEPTED_IMAGE_TYPES } from '@/validation/schemas/common.schema';
import { AggregateQueryManager } from '@/utils/aggregate.utils';

export const createChildProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const validatedData = await validateData(createChildProfileSchema, req.body);

  let avatarUrl: string | undefined;

  if (req.files && req.files.avatar) {
    const avatar = req.files.avatar;
    const file = Array.isArray(avatar) ? avatar[0] : avatar;

    if (file.size > MAX_FILE_SIZE) {
      throw new BadRequestError('File size must be less than 2MB');
    }

    if (!ACCEPTED_IMAGE_TYPES.includes(file.mimetype)) {
      throw new BadRequestError('File must be a valid image (JPEG, PNG, JPG, WEBP)');
    }

    const fileUploader = createFileUploader('image');
    const directory = `avatars/${userId}`;
    const fileName = `avatar-${Date.now()}`;

    avatarUrl = await fileUploader.uploadFile({ file, fileName, directory });
  }

  const childProfile = await ChildProfile.create({ ...validatedData, userId, avatar: avatarUrl || '' });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Child profile created successfully',
    data: { childProfile },
  });
};

export const getChildProfiles = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const queryString = req.query;

  const baseAggregation = [{ $match: { userId: new mongoose.Types.ObjectId(userId), isActive: true } }];

  const queryManager = new AggregateQueryManager({ model: ChildProfile, queryString, baseAggregation }).filter().sort().paginate();

  const [childProfiles, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Child profiles fetched successfully',
    data: { childProfiles, pagination },
  });
};

export const getChildProfileById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid child profile ID');
  }

  const childProfile = await ChildProfile.findOne({ _id: id, userId, isActive: true });

  if (!childProfile) {
    throw new NotFoundError(`No child profile found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({ success: true, message: 'Child profile fetched successfully', data: { childProfile } });
};

export const updateChildProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const { id } = req.params;

  const updateData = req.body as UpdateChildProfileInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid child profile ID');
  }

  if (Object.keys(updateData).length === 0 && !req.files) {
    throw new BadRequestError('Please provide data to update');
  }

  const childProfile = await ChildProfile.findOne({ _id: id, userId, isActive: true });

  if (!childProfile) {
    throw new NotFoundError(`No child profile found with id: ${id}`);
  }

  const validatedData = await validateData(createChildProfileSchema.partial(), updateData);

  if (req.files && req.files.avatar) {
    const avatar = req.files.avatar;

    const file = Array.isArray(avatar) ? avatar[0] : avatar;

    if (file.size > MAX_FILE_SIZE) {
      throw new BadRequestError('File size must be less than 2MB');
    }

    if (!ACCEPTED_IMAGE_TYPES.includes(file.mimetype)) {
      throw new BadRequestError('File must be a valid image (JPEG, PNG, JPG, WEBP)');
    }

    const fileUploader = createFileUploader('image');
    const directory = `avatars/${childProfile._id.toString()}`;
    const fileName = `avatar-${Date.now()}`;

    const avatarUrl = await fileUploader.uploadFile({ file, fileName, directory });
    validatedData.avatar = avatarUrl;
  }

  const updatedChildProfile = await ChildProfile.findOneAndUpdate({ _id: id, userId, isActive: true }, validatedData, {
    new: true,
    runValidators: true,
  });

  res.status(StatusCodes.OK).json({ success: true, message: 'Child profile updated successfully', data: { childProfile: updatedChildProfile } });
};

export const deleteChildProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid child profile ID');
  }

  const childProfile = await ChildProfile.findOne({ _id: id, userId, isActive: true });

  if (!childProfile) {
    throw new NotFoundError(`No child profile found with id: ${id}`);
  }

  childProfile.isActive = false;
  await childProfile.save();

  res.status(StatusCodes.OK).json({ success: true, message: 'Child profile deleted successfully', data: null });
};
