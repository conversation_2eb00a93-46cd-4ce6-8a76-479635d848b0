# Educational Service Categories Documentation

## Overview

The educational platform is organized into seven main service categories, each representing a distinct educational domain. This document provides an overview of all service categories and their hierarchical structures.

## Service Categories

The platform includes the following service categories:

1. **Schools** - For K-12 education organized by boards, classes, and subjects
2. **Colleges** - For higher education organized by streams, degree levels, degrees, branches, and subjects
3. **Languages** - For language learning organized by language types and languages
4. **Hobbies** - For hobby-related education organized by hobby types and hobbies
5. **IT Courses** - For technology education organized by course types and courses
6. **Competitive Exams** - For competitive exam preparation organized by exam categories, exams, and subjects
7. **Entrance Exams** - For entrance exam preparation organized by exam categories, exams, and subjects

## Hierarchical Structure

Each service category follows its own hierarchical structure:

```
Schools
├── Board (CBSE, ICSE, IB, State Board, DEV)
│   ├── Class (Nursery-KG, Class I, Class II, ..., Class XII)
│   │   ├── Subject (Mathematics, Science, English, etc.)

Colleges
├── Stream (Engineering, Medical, Arts, Commerce, Science, Management, Law, Other)
│   ├── Degree Level (Graduation, Post Graduation, Diploma, PhD)
│   │   ├── Degree (B.Tech, M.Sc, BBA, etc.)
│   │   │   ├── Branch (Computer Science, Mechanical, etc.)
│   │   │   │   ├── Subject (Data Structures, Thermodynamics, etc.)

Languages
├── Language Type (Indian, Foreign)
│   ├── Language (Hindi, English, French, German, etc.)

Hobbies
├── Hobby Type (Music, Art, Sports, Cooking, etc.)
│   ├── Hobby (Guitar, Painting, Cricket, Baking, etc.)

IT Courses
├── Course Type (Web Development, App Development, Data Science, etc.)
│   ├── Course (React.js, Flutter, Python for Data Science, etc.)

Exams (Competitive/Entrance)
├── Exam Category (Engineering, Medical, Management, Law, Civil Services, etc.)
│   ├── Exam (JEE, NEET, CAT, CLAT, UPSC, etc.)
│   │   ├── Exam Subject (Physics, Chemistry, Mathematics, Biology, etc.)
```

## Data Models

Each service category has its own set of models that represent the entities in its hierarchy. All models follow a consistent pattern:

- Each entity has a name field (either string or enum)
- Each entity has an isActive flag for soft deletion
- Each entity has timestamps (createdAt, updatedAt)
- Child entities reference their parent entities via ObjectId
- Appropriate indexes are defined for efficient queries

## API Structure

The API follows a consistent pattern for all service categories:

- GET endpoints for retrieving all entities of a type
- GET endpoints for retrieving entities by their parent
- GET endpoints for retrieving a specific entity by ID
- POST endpoints for creating new entities (admin only)
- PATCH endpoints for updating entities (admin only)
- DELETE endpoints for deleting entities (super_admin only)

## Implementation Details

### Common Features

- All service categories are accessible through the `/api/v1/education` base path
- Authentication and authorization are handled consistently across all endpoints
- Validation ensures data integrity and hierarchical relationships
- Pagination, sorting, and filtering are available for list endpoints

### Seed Data

The platform includes seed data for:

- All service categories
- Predefined enums (boards, streams, degree levels, language types, course types, exam categories)
- Basic hierarchical structures

## Detailed Documentation

For detailed information about each service category, refer to the following documents:

- [Schools](./school.md)
- [Colleges](./college.md)
- [Languages](./languages.md)
- [Hobbies](./hobbies.md)
- [IT Courses](./it-courses.md)
- [Exams](./exams.md)

## Usage in the Platform

The service categories provide the foundation for:

1. **User Profiles**: Tutors and students can specify their areas of interest/expertise
2. **Content Organization**: Educational materials are categorized according to these hierarchies
3. **Search and Discovery**: Users can browse and search for content or tutors by navigating these hierarchies
4. **Analytics**: Usage and performance data can be aggregated at different levels of these hierarchies

## Future Extensions

The service category system is designed to be extensible:

- New categories can be added as needed
- Existing hierarchies can be extended with additional levels
- New attributes can be added to entities without disrupting the basic structure
