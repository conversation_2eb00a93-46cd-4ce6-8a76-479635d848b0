import mongoose, { Document, Schema } from 'mongoose';
import { addressTypeMap } from '@/validation/schemas/maps';
import { AddressFormValues } from '@/validation/schemas/user.schema';

export interface IAddress extends AddressFormValues {
  user: mongoose.Types.ObjectId;
}

export interface AddressDocument extends IAddress, Document {
  createdAt: Date;
  updatedAt: Date;
}

const addressSchema = new Schema<AddressDocument>(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    houseNo: {
      type: String,
      required: [true, 'House number is required'],
      trim: true,
    },
    locality: {
      type: String,
      required: [true, 'Locality is required'],
      trim: true,
    },
    landmark: {
      type: String,
      trim: true,
    },
    areaPinCode: {
      type: String,
      required: [true, 'PIN/ZIP code is required'],
      trim: true,
      minlength: [3, 'PIN/ZIP code must be at least 3 characters long'],
      maxlength: [12, 'PIN/ZIP code cannot exceed 12 characters'],
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true,
    },
    district: {
      type: String,
      required: [true, 'District is required'],
      trim: true,
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true,
    },
    addressType: {
      type: String,
      enum: Object.keys(addressTypeMap),
      required: [true, 'Address type is required'],
    },
    lat: Number,
    lng: Number,
  },
  {
    timestamps: true,
  }
);

addressSchema.index({ user: 1, addressType: 1 }, { unique: true });

const Address = mongoose.models.Address || mongoose.model<AddressDocument>('Address', addressSchema);

export default Address;
