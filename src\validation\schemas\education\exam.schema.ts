import { z } from 'zod';
import { objectIdSchema } from '../common.schema';

// Exam Category Schemas
export const createExamCategorySchema = z.object({
  name: z.string({ required_error: 'Exam category name is required' }).min(1, 'Exam category name is required'),
  isActive: z.boolean().default(true),
});

export type CreateExamCategoryInput = z.infer<typeof createExamCategorySchema>;
export type UpdateExamCategoryInput = Partial<CreateExamCategoryInput>;

// Exam Schemas
export const createExamSchema = z.object({
  name: z.string({ required_error: 'Exam name is required' }).min(1, 'Exam name is required'),
  examCategory: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateExamInput = z.infer<typeof createExamSchema>;
export type UpdateExamInput = Partial<CreateExamInput>;

// Exam Subject Schemas
export const createExamSubjectSchema = z.object({
  name: z.string({ required_error: 'Exam subject name is required' }).min(1, 'Exam subject name is required'),
  exam: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateExamSubjectInput = z.infer<typeof createExamSubjectSchema>;
export type UpdateExamSubjectInput = Partial<CreateExamSubjectInput>;
