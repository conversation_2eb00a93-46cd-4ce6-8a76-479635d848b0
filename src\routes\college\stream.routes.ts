import express from 'express';
import { createStream, getAllStreams, getStreamById, updateStream, deleteStream } from '@/controllers/college/stream.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllStreams);
router.get('/:id', getStreamById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createStream);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateStream);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteStream);

export default router;
