import mongoose, { Document, Schema } from 'mongoose';
import { CreateCourseInput } from '@/validation/schemas/education/course.schema';

export interface ICourse extends Omit<CreateCourseInput, 'courseType'> {
  courseType: mongoose.Types.ObjectId;
}

export interface CourseDocument extends ICourse, Document {
  createdAt: Date;
  updatedAt: Date;
}

const courseSchema = new Schema<CourseDocument>(
  {
    name: {
      type: String,
      required: [true, 'Course name is required'],
      trim: true,
    },
    courseType: {
      type: Schema.Types.ObjectId,
      ref: 'CourseType',
      required: [true, 'Course type is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
courseSchema.index({ courseType: 1, name: 1 }, { unique: true });

const Course = mongoose.models.Course || mongoose.model<CourseDocument>('Course', courseSchema);

export default Course;
