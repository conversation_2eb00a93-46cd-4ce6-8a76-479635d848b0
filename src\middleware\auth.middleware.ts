import type { Response, NextFunction } from 'express';
import { UnauthenticatedError, UnauthorizedError } from '@/errors';
import { validateSession, ISessionUser, extractTokenFromHeader } from '@/utils/auth.utils';
import Session from '@/models/session.model';
import type { AuthenticatedRequest } from '@/types/express';

export const authenticatedUser = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  const token = extractTokenFromHeader(req);
  const cookieToken = req.signedCookies.session;
  const authToken = token || cookieToken;

  try {
    if (!authToken) throw new UnauthenticatedError('Authentication required - no valid token found');

    const payload = await validateSession(authToken);

    if (payload.sessionId) {
      const dbSession = await Session.findOne({
        sessionId: payload.sessionId,
        user: payload.user.userId,
        userType: 'user',
        isValid: true,
      });

      if (!dbSession) {
        throw new UnauthenticatedError('Session has been invalidated');
      }

      if (dbSession.expiresAt < new Date()) {
        throw new UnauthenticatedError('Session has expired');
      }
    }

    req.user = payload.user;
    req.sessionId = payload.sessionId;

    next();
  } catch (err) {
    console.error('Authentication error:', err);

    if (cookieToken) {
      res.cookie('session', 'logout', { httpOnly: true, expires: new Date(Date.now()) });
    }

    throw new UnauthenticatedError('Authentication failed - please log in again');
  }
};

export const authorizePermissions = (...allowedUserTypes: string[]) => {
  return (req: AuthenticatedRequest, _res: Response, next: NextFunction): void => {
    if (!req.user) throw new UnauthenticatedError('Authentication required');

    if (allowedUserTypes.includes(req.user.userType)) {
      return next();
    }

    throw new UnauthorizedError('Not authorized, you do not have the required permissions');
  };
};

export const checkPermissions = (requestUser: ISessionUser, resourceUserId: string): void => {
  const resourceId = resourceUserId.toString();
  if (requestUser.userId === resourceId) {
    return;
  }

  throw new UnauthorizedError('Not authorized to access this resource, you do not have the required permissions');
};
