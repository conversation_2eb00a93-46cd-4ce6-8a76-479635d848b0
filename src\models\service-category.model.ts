import mongoose, { Document, Schema } from 'mongoose';
import { CreateServiceCategoryInput } from '@/validation/schemas/service-category.schema';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';

export interface IServiceCategory extends CreateServiceCategoryInput {}

export interface ServiceCategoryDocument extends IServiceCategory, Document {
  createdAt: Date;
  updatedAt: Date;
}

const serviceCategorySchema = new Schema<ServiceCategoryDocument>(
  {
    name: {
      type: String,
      enum: Object.keys(serviceCategoryMap),
      required: [true, 'Service category name is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
serviceCategorySchema.index({ name: 1 });

const ServiceCategory = mongoose.models.ServiceCategory || mongoose.model<ServiceCategoryDocument>('ServiceCategory', serviceCategorySchema);

export default ServiceCategory;
