import nodemailer, { TransportOptions } from 'nodemailer';

interface SmtpConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
}

interface PasswordResetEmailParams {
  fullName: string;
  email: string;
  resetPasswordToken: string;
  origin: string;
}

interface VerificationEmailParams {
  fullName: string;
  email: string;
  verificationToken: string;
  origin: string;
}

const smtpConfig: SmtpConfig = {
  host: process.env.MAIL_HOST!,
  port: Number(process.env.MAIL_PORT!),
  secure: Number(process.env.MAIL_PORT!) === 465,
  auth: {
    user: process.env.MAIL_USER!,
    pass: process.env.MAIL_PASS!,
  },
};

export const sendEmail = async ({ to, subject, html }: EmailOptions): Promise<void> => {
  const transporter = nodemailer.createTransport(smtpConfig as TransportOptions);

  await transporter.sendMail({ from: `"perfecttutor" <${process.env.MAIL_USER!}>`, to, subject, html });
};

export const sendResetPasswordEmail = async ({ fullName, email, resetPasswordToken, origin }: PasswordResetEmailParams): Promise<void> => {
  const verificationLink = `${origin}/api/v1/auth/reset-pass?token=${resetPasswordToken}&email=${email}`;

  const message = `<h4>Hello ${fullName}</h4>
    <p>Please reset your password by <a href="${verificationLink}">clicking here</a></p>`;

  await sendEmail({ to: email, subject: 'Reset your password', html: message });
};

export const sendVerificationEmail = async ({ fullName, email, verificationToken, origin }: VerificationEmailParams): Promise<void> => {
  const verificationLink = `${origin}/api/v1/auth/verify-email?token=${verificationToken}&email=${email}`;

  const message = `<h4>Hello ${fullName}</h4>
    <p>Please verify your email address by <a href="${verificationLink}">clicking here</a></p>`;

  await sendEmail({ to: email, subject: 'Confirm your email address', html: message });
};
