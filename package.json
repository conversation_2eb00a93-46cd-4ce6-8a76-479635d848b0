{"name": "@perfecttutor/api", "version": "1.0.0", "description": "Backend API for perfecttutor education platform", "main": "dist/server.js", "scripts": {"start": "cross-env NODE_ENV=production node -r tsconfig-paths/register -r ts-node/register/transpile-only ./dist/server.js", "dev": "cross-env NODE_ENV=development nodemon", "build": "node build.mjs", "prestart": "npm run build", "seed:education": "ts-node -r tsconfig-paths/register src/scripts/seed-education-data.ts"}, "nodemonConfig": {"watch": ["src"], "ext": "ts", "ignore": ["src/**/*.spec.ts"], "exec": "ts-node -r tsconfig-paths/register src/server.ts"}, "keywords": ["node", "api", "express", "typescript"], "author": "", "license": "ISC", "dependencies": {"@types/morgan": "^1.9.9", "bcryptjs": "^2.4.3", "chalk": "^5.4.1", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.5.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "morgan": "^1.10.0", "nodemailer": "^6.10.0", "openai": "^4.96.1", "slugify": "^1.6.6", "validator": "^13.15.0", "zod": "^3.24.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/dotenv": "^6.1.1", "@types/express": "^5.0.1", "@types/express-fileupload": "^1.5.1", "@types/express-mongo-sanitize": "^1.3.2", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/hpp": "^0.2.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.14.0", "@types/nodemailer": "^6.4.17", "@types/validator": "^13.12.3", "cross-env": "^7.0.3", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "tsc-alias": "^1.8.15", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.3", "typescript": "^5.8.3"}}