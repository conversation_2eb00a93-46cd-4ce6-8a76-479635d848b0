import { hashString } from './crypto.utils';
import jwt from 'jsonwebtoken';
import type { Response, Request } from 'express';
import { UnauthenticatedError } from '@/errors';
import { UserDocument } from '@/models/user.model';
import { IAccountStatusMap, IGenderMap, IUserTypeMap } from '@/validation/schemas/maps';

export interface ISessionUser {
  userId: string;
  userType: IUserTypeMap;
  fullName: string;
  profilePicture?: string;
  dateOfBirth?: Date;
  gender?: IGenderMap;
  email?: string;
  phone?: string;
  accountStatus: IAccountStatusMap;
}

export interface JWTPayload {
  user: ISessionUser;
  sessionId?: string;
  iat?: number;
  exp?: number;
}

export interface TokenPayload {
  user: ISessionUser;
  sessionId?: string;
}

interface CookieOptions {
  httpOnly: boolean;
  secure: boolean;
  signed: boolean;
  sameSite: 'strict' | 'lax' | 'none';
  maxAge?: number;
  domain?: string;
  path?: string;
  expires?: Date;
}

export const createHash = (input: string): string => hashString(input);

export const createSessionUser = async (user: UserDocument): Promise<ISessionUser> => {
  return {
    userId: user._id ? user._id.toString() : '',
    fullName: user.fullName || '',
    email: user.email || '',
    phone: user.phone || '',
    profilePicture: user.profilePicture,
    userType: user.userType,
    dateOfBirth: user.dateOfBirth,
    gender: user.gender,
    accountStatus: user.accountStatus,
  };
};

export const createToken = ({ payload, expiresIn }: { payload: TokenPayload; expiresIn: string }): string => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  const secret = process.env.JWT_SECRET as jwt.Secret;
  return jwt.sign(payload, secret, {
    expiresIn,
    algorithm: 'HS256',
    issuer: 'perfecttutor-api',
  } as jwt.SignOptions);
};

export const validateSession = async (token: string): Promise<JWTPayload> => {
  try {
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }

    const secret = process.env.JWT_SECRET as jwt.Secret;
    const payload = jwt.verify(token, secret, {
      algorithms: ['HS256'],
      issuer: 'perfecttutor-api',
    }) as JWTPayload;

    if (!payload.user || !payload.user.userId) {
      throw new Error('Session payload is missing required fields');
    }

    return payload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new UnauthenticatedError('Session expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new UnauthenticatedError('Invalid session');
    }
    throw new UnauthenticatedError('Authentication invalid');
  }
};

export const createSessionCookie = async ({ res, user, sessionId }: { res: Response; user: ISessionUser; sessionId: string }): Promise<string> => {
  try {
    const sessionLifetime = process.env.SESSION_LIFETIME || '7d';
    const domain = process.env.APP_DOMAIN || '.perfecttutor.site';

    const sessionToken = createToken({
      payload: { user, sessionId },
      expiresIn: sessionLifetime,
    });

    const sessionMaxAge = Number.parseInt(process.env.SESSION_MAX_AGE!, 10) || 604800000;

    const cookieOptions: CookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      signed: true,
      sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
      path: '/',
      maxAge: sessionMaxAge,
    };

    if (process.env.NODE_ENV === 'production') {
      Object.assign(cookieOptions, { domain, expires: new Date(Date.now() + sessionMaxAge) });
    }

    res.cookie('session', sessionToken, cookieOptions);

    return sessionToken;
  } catch (error) {
    console.error('Error creating session cookie:', error);
    throw error;
  }
};

export const extractTokenFromHeader = (req: Request): string | null => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  return authHeader.split(' ')[1];
};
