import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import { validateData } from '@/utils';
import { AuthenticatedRequest } from '@/types/express';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';

// Models
import TeachingSubject from '@/models/tutor/teaching-subject.model';

// Schemas
import { createTeachingSubjectSchema, updateTeachingSubjectSchema } from '@/validation/schemas/tutor/profiles/teaching-subjects.schema';
import { AggregateQueryManager } from '@/utils/aggregate.utils';

const teachingSubjectAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'boards',
      localField: 'boardId',
      foreignField: '_id',
      as: 'boardDetails',
    },
  },
  {
    $lookup: {
      from: 'classes',
      localField: 'classId',
      foreignField: '_id',
      as: 'classDetails',
    },
  },
  {
    $lookup: {
      from: 'subjects',
      localField: 'subjectIds',
      foreignField: '_id',
      as: 'subjectDetails',
    },
  },
  // College lookups
  {
    $lookup: {
      from: 'streams',
      localField: 'streamId',
      foreignField: '_id',
      as: 'streamDetails',
    },
  },
  {
    $lookup: {
      from: 'degreelevels',
      localField: 'degreeLevelId',
      foreignField: '_id',
      as: 'degreeLevelDetails',
    },
  },
  {
    $lookup: {
      from: 'degrees',
      localField: 'degreeId',
      foreignField: '_id',
      as: 'degreeDetails',
    },
  },
  {
    $lookup: {
      from: 'branches',
      localField: 'branchId',
      foreignField: '_id',
      as: 'branchDetails',
    },
  },
  {
    $lookup: {
      from: 'collegesubjects',
      localField: 'collegeSubjectIds',
      foreignField: '_id',
      as: 'collegeSubjectDetails',
    },
  },
  // Language lookups
  {
    $lookup: {
      from: 'languagetypes',
      localField: 'languageTypeId',
      foreignField: '_id',
      as: 'languageTypeDetails',
    },
  },
  {
    $lookup: {
      from: 'languages',
      localField: 'languageId',
      foreignField: '_id',
      as: 'languageDetails',
    },
  },
  // Hobby lookups
  {
    $lookup: {
      from: 'hobbytypes',
      localField: 'hobbyTypeId',
      foreignField: '_id',
      as: 'hobbyTypeDetails',
    },
  },
  {
    $lookup: {
      from: 'hobbies',
      localField: 'hobbyId',
      foreignField: '_id',
      as: 'hobbyDetails',
    },
  },
  // Exam lookups
  {
    $lookup: {
      from: 'examcategories',
      localField: 'examCategoryId',
      foreignField: '_id',
      as: 'examCategoryDetails',
    },
  },
  {
    $lookup: {
      from: 'exams',
      localField: 'examId',
      foreignField: '_id',
      as: 'examDetails',
    },
  },
  {
    $lookup: {
      from: 'examsubjects',
      localField: 'examSubjectIds',
      foreignField: '_id',
      as: 'examSubjectDetails',
    },
  },
  // IT Course lookups
  {
    $lookup: {
      from: 'coursetypes',
      localField: 'courseTypeId',
      foreignField: '_id',
      as: 'courseTypeDetails',
    },
  },
  {
    $lookup: {
      from: 'courses',
      localField: 'courseId',
      foreignField: '_id',
      as: 'courseDetails',
    },
  },
  // Unwind single document arrays
  {
    $unwind: {
      path: '$boardDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$classDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$streamDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$degreeLevelDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$degreeDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$branchDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$languageTypeDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$languageDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$hobbyTypeDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$hobbyDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$examCategoryDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$examDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$courseTypeDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $unwind: {
      path: '$courseDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  { $sort: { createdAt: -1 } },
];

export const getAllTeachingSubjects = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user!.userId;
  const queryString = req.query;

  const matchStage = { userId: new mongoose.Types.ObjectId(userId) };
  const baseAggregation = [{ $match: matchStage }, ...teachingSubjectAggregation];

  const queryManager = new AggregateQueryManager({ model: TeachingSubject, queryString, baseAggregation }).filter().paginate();

  const [teachingSubjects, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Teaching subjects fetched successfully',
    data: { teachingSubjects, pagination },
  });
};

export const createTeachingSubject = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createTeachingSubjectSchema, req.body);
  const userId = req.user!.userId;

  const teachingSubject = await TeachingSubject.create({ ...validatedData, userId });

  if (!teachingSubject) throw new Error('Teaching subject not created');

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Teaching subject created successfully',
    data: { teachingSubject },
  });
};

export const updateTeachingSubject = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const userId = req.user!.userId;

  const validatedData = await validateData(updateTeachingSubjectSchema, req.body);

  if (!isValidObjectId(id)) throw new BadRequestError('Invalid teaching subject ID');

  const teachingSubject = await TeachingSubject.findOneAndUpdate({ _id: id, userId }, { ...validatedData }, { new: true, runValidators: true });

  if (!teachingSubject) throw new NotFoundError('Teaching subject not found');

  res.status(StatusCodes.OK).json({ success: true, message: 'Teaching subject updated successfully', data: { teachingSubject } });
};

export const deleteTeachingSubject = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const userId = req.user!.userId;
  if (!isValidObjectId(id)) {
    throw new BadRequestError('Invalid teaching subject ID');
  }
  const teachingSubject = await TeachingSubject.findOneAndDelete({ _id: id, userId });
  if (!teachingSubject) {
    throw new NotFoundError('Teaching subject not found');
  }
  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Teaching subject deleted successfully',
    data: { teachingSubject },
  });
};
