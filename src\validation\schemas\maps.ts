import { createOptionsKeyMap } from '@/validation/utils/form.utils';

// 1. Gender
export const genderMap = {
  male: { key: 'male', label: 'Male' },
  female: { key: 'female', label: 'Female' },
  other: { key: 'other', label: 'Other' },
} as const;

export const genderOptions = createOptionsKeyMap(genderMap);
export type IGenderMap = keyof typeof genderMap;

// 2. User Types

// 2A Student or Tutor
export const studentOrTutorGroupMap = {
  student: { key: 'student', label: 'Student' },
  tutor: { key: 'tutor', label: 'Tutor' },
} as const;

// 2B Institutions
export const institutionGroupMap = {
  institute: { key: 'institute', label: 'Institute' },
  school: { key: 'school', label: 'School' },
  college: { key: 'college', label: 'College' },
} as const;

export const userTypeMap = { ...studentOrTutorGroupMap, ...institutionGroupMap } as const;

export type IUserTypeMap = keyof typeof userTypeMap;
export type IStudentOrTutorGroup = keyof typeof studentOrTutorGroupMap;
export type IInstituteGroup = keyof typeof institutionGroupMap;

// 3. Account Status
export const accountStatusMap = {
  pending: { key: 'pending', label: 'Pending' },
  incomplete: { key: 'incomplete', label: 'Incomplete' },
  active: { key: 'active', label: 'Active' },
  hold: { key: 'hold', label: 'On Hold' },
  blocked: { key: 'blocked', label: 'Blocked' },
} as const;

export type IAccountStatusMap = keyof typeof accountStatusMap;

// 4. Admin Roles
export const adminRolesMap = {
  super_admin: { key: 'super_admin', label: 'Super Admin' },
  admin: { key: 'admin', label: 'Admin' },
  telecaller: { key: 'telecaller', label: 'Telecaller' },
  billing: { key: 'billing', label: 'Billing' },
} as const;

export type IAdminRolesMap = keyof typeof adminRolesMap;

// 5. Referral Sources
export const referralSourcesMap = {
  google_search: { key: 'google_search', label: 'Google Search' },
  social_media: { key: 'social_media', label: 'Social Media' },
  friend_family: { key: 'friend_family', label: 'Friend/Family' },
  advertisement: { key: 'advertisement', label: 'Advertisement' },
  other: { key: 'other', label: 'Other' },
} as const;

export const referralSourcesWhichRequiresOther = ['friend_family', 'other'] as const;
export type IReferralSourcesWhichRequiresOther = (typeof referralSourcesWhichRequiresOther)[number];

export const referralSourcesOptions = createOptionsKeyMap(referralSourcesMap);
export type IReferralSources = keyof typeof referralSourcesMap;

// 6. Address Types
export const addressTypeMap = {
  current: { key: 'current', label: 'Current Address' },
  permanent: { key: 'permanent', label: 'Permanent Address' },
} as const;

export const addressTypeOptions = createOptionsKeyMap(addressTypeMap);
export type IAddressTypeMap = keyof typeof addressTypeMap;
