import mongoose, { Document, Schema } from 'mongoose';
import { deliveryModeMap, IDeliveryModeMap } from '@/validation/schemas/enquiry.maps';
import { CreateTuitionInfoInput } from '@/validation/schemas/tutor/profiles/tuition-info.schema';

export interface ITuitionInfo extends CreateTuitionInfoInput {
  userId: mongoose.Types.ObjectId;
}

export interface TuitionInfoDocument extends ITuitionInfo, Document {
  createdAt: Date;
  updatedAt: Date;
}

const tuitionInfoSchema = new Schema<TuitionInfoDocument>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      unique: true,
    },
    totalTeachingExperience: {
      type: Number,
      required: [true, 'Total teaching experience is required'],
      min: [0, 'Teaching experience must be non-negative'],
    },
    isFullTimeTeacher: {
      type: Boolean,
      required: [true, 'Full-time teacher status is required'],
    },
    teachesSpecialStudents: {
      type: Boolean,
      required: [true, 'Special students teaching status is required'],
    },
    maxTravelDistance: {
      type: Number,
      required: [true, 'Maximum travel distance is required'],
      min: [0, 'Travel distance must be non-negative'],
    },
    spokenLanguages: {
      type: [String],
      required: [true, 'At least one spoken language is required'],
      validate: {
        validator: function (languages: string[]) {
          return languages.length > 0;
        },
        message: 'At least one language must be selected',
      },
    },
    deliveryModes: {
      type: [String],
      enum: Object.keys(deliveryModeMap),
      required: [true, 'At least one delivery mode is required'],
      validate: {
        validator: function (modes: IDeliveryModeMap[]) {
          return modes.length > 0;
        },
        message: 'At least one delivery mode must be selected',
      },
    },
    location: {
      type: String,
      required: [true, 'Location is required'],
      trim: true,
    },
    coordinates: {
      type: {
        lat: Number,
        lng: Number,
      },
      _id: false,
    },
    description: {
      type: String,
      required: [true, 'Description is required'],
      trim: true,
      minlength: [10, 'Description must be at least 10 characters'],
      maxlength: [1000, 'Description cannot exceed 1000 characters'],
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
tuitionInfoSchema.index({ deliveryModes: 1 });
tuitionInfoSchema.index({ location: 1 });

const TuitionInfo = mongoose.models.TuitionInfo || mongoose.model<TuitionInfoDocument>('TuitionInfo', tuitionInfoSchema);

export default TuitionInfo;
