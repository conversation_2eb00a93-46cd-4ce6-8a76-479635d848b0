import express from 'express';
import { login, register, logout, forgotPassword, resetPassword, refreshToken, requestOTP, verifyOTP } from '@/controllers/auth.controller';
import { authenticatedUser } from '@/middleware/auth.middleware';
import { authLimiter, passwordResetLimiter } from '@/middleware/rate-limit.middleware';

const router = express.Router();

// Public routes
router.post('/register', register);
router.post('/login', authLimiter, login);
router.post('/forgot-password', passwordResetLimiter, forgotPassword);
router.post('/reset-password', passwordResetLimiter, resetPassword);

// OTP authentication routes
router.post('/request-otp', authLimiter, requestOTP);
router.post('/verify-otp', verifyOTP);

// Protected routes
router.delete('/logout', authenticatedUser, logout);

router.post('/refresh', refreshToken);

export default router;
