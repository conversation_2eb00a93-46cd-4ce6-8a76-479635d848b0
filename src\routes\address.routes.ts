import express from 'express';
import { createAddress, getUserAddresses, getAddressById, updateAddress, deleteAddress } from '@/controllers/address.controller';
import { authenticatedUser } from '@/middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use(authenticatedUser);

router.route('/').post(createAddress).get(getUserAddresses);
router.route('/:addressId').get(getAddressById).patch(updateAddress).delete(deleteAddress);

export default router;
