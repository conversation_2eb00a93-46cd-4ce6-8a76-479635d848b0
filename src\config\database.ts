import mongoose from 'mongoose';
import { colorizer } from '@/utils';

const dbConnect = async (url: string): Promise<typeof mongoose> => {
  try {
    mongoose.set('strictQuery', false);
    const conn = await mongoose.connect(url);
    colorizer.success(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    colorizer.error('Error connecting to MongoDB:');
    console.error(error);
    process.exit(1);
  }
};

export default dbConnect;
