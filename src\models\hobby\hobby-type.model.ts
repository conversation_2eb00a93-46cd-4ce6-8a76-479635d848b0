import mongoose, { Document, Schema } from 'mongoose';
import { CreateHobbyTypeInput } from '@/validation/schemas/education/hobby.schema';

export interface IHobbyType extends CreateHobbyTypeInput {}

export interface HobbyTypeDocument extends IHobbyType, Document {
  createdAt: Date;
  updatedAt: Date;
}

const hobbyTypeSchema = new Schema<HobbyTypeDocument>(
  {
    name: {
      type: String,
      required: [true, 'Hobby type name is required'],
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
hobbyTypeSchema.index({ name: 1 });

const HobbyType = mongoose.models.HobbyType || mongoose.model<HobbyTypeDocument>('HobbyType', hobbyTypeSchema);

export default HobbyType;
