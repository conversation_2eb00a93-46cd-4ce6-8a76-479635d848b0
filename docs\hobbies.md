# Hobbies Service Category Documentation

## Overview

The Hobbies service category is one of the seven main educational domains in the platform. It provides a structured way to organize various hobby-related educational content and services.

## Hierarchy

The Hobbies service category follows a two-level hierarchy:

1. **Hobby Type** - The category of hobby (e.g., Music, Art, Sports)
2. **Hobby** - The specific hobby within a category (e.g., Guitar, Painting, Cricket)

```
Hobbies
├── Hobby Type (Music, Art, Sports, Cooking, etc.)
│   ├── <PERSON><PERSON> (Guitar, Painting, Cricket, Baking, etc.)
```

## Data Models

### Hobby Type Model

The Hobby Type model represents categories of hobbies.

```typescript
// src/models/hobby/hobby-type.model.ts
export interface IHobbyType {
  name: string;
  isActive: boolean;
}

// Schema definition
const hobbyTypeSchema = new Schema<HobbyTypeDocument>({
  name: {
    type: String,
    required: [true, 'Hobby type name is required'],
    trim: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
hobbyTypeSchema.index({ name: 1 });
```

### Hobby Model

The Hobby model represents specific hobbies within a hobby type.

```typescript
// src/models/hobby/hobby.model.ts
export interface IHobby {
  name: string;
  hobbyType: mongoose.Types.ObjectId; // Reference to HobbyType
  isActive: boolean;
}

// Schema definition
const hobbySchema = new Schema<HobbyDocument>({
  name: {
    type: String,
    required: [true, 'Hobby name is required'],
    trim: true,
  },
  hobbyType: {
    type: Schema.Types.ObjectId,
    ref: 'HobbyType',
    required: [true, 'Hobby type is required'],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
hobbySchema.index({ hobbyType: 1, name: 1 }, { unique: true });
```

## API Endpoints

### Hobby Type Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/hobby-types` | Get all hobby types | No |
| GET | `/api/v1/education/hobby-types/:id` | Get a specific hobby type | No |
| POST | `/api/v1/education/hobby-types` | Create a new hobby type | Yes (admin) |
| PATCH | `/api/v1/education/hobby-types/:id` | Update a hobby type | Yes (admin) |
| DELETE | `/api/v1/education/hobby-types/:id` | Delete a hobby type | Yes (super_admin) |

### Hobby Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/hobbies` | Get all hobbies | No |
| GET | `/api/v1/education/hobbies/type/:hobbyTypeId` | Get hobbies by type | No |
| GET | `/api/v1/education/hobbies/:id` | Get a specific hobby | No |
| POST | `/api/v1/education/hobbies` | Create a new hobby | Yes (admin) |
| PATCH | `/api/v1/education/hobbies/:id` | Update a hobby | Yes (admin) |
| DELETE | `/api/v1/education/hobbies/:id` | Delete a hobby | Yes (super_admin) |

## Example Hobby Types and Hobbies

### Music
- Guitar
- Piano
- Violin
- Singing
- Drums

### Art
- Painting
- Sketching
- Sculpture
- Photography
- Calligraphy

### Sports
- Cricket
- Football
- Basketball
- Tennis
- Swimming

### Cooking
- Baking
- Grilling
- International Cuisine
- Desserts
- Vegetarian Cooking

### Dance
- Classical
- Contemporary
- Hip-hop
- Ballroom
- Folk

## Data Flow

1. **Creating a Hobby Type**: Admin creates a hobby type (e.g., Music)
2. **Creating Hobbies**: Admin creates hobbies within that type (e.g., Guitar, Piano, Violin)

## Usage Examples

### Creating a Hobby Type

```javascript
// POST /api/v1/education/hobby-types
{
  "name": "Music"
}
```

### Creating a Hobby

```javascript
// POST /api/v1/education/hobbies
{
  "name": "Guitar",
  "hobbyType": "60d5ec9af682d123e4567890" // Hobby Type ID
}
```

### Getting Hobbies by Type

```
GET /api/v1/education/hobbies/type/60d5ec9af682d123e4567890
```

## Relationships with Other Entities

- **Users**: Tutors can specify which hobbies they teach
- **Students**: Students can express interest in specific hobbies
- **Content**: Educational content can be categorized by hobby
- **Classes/Courses**: Classes and courses can be organized by hobby

## Implementation Notes

- The hierarchy is enforced through validation in the controllers
- When creating a hobby, the hobby type must exist
- All entities have an `isActive` flag to enable/disable without deletion
- Hobby types can be extended as needed to accommodate new categories
