import mongoose, { Document, Schema } from 'mongoose';
import { CreateDegreeInput } from '@/validation/schemas/education/college.schema';

export interface IDegree extends Omit<CreateDegreeInput, 'degreeLevel'> {
  degreeLevel: mongoose.Types.ObjectId;
}

export interface DegreeDocument extends IDegree, Document {
  createdAt: Date;
  updatedAt: Date;
}

const degreeSchema = new Schema<DegreeDocument>(
  {
    name: {
      type: String,
      required: [true, 'Degree name is required'],
      trim: true,
    },
    degreeLevel: {
      type: Schema.Types.ObjectId,
      ref: 'DegreeLevel',
      required: [true, 'Degree level is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
degreeSchema.index({ degreeLevel: 1, name: 1 }, { unique: true });

const Degree = mongoose.models.Degree || mongoose.model<DegreeDocument>('Degree', degreeSchema);

export default Degree;
