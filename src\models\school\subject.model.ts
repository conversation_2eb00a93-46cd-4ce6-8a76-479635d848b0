import { CreateSubjectInput } from '@/validation/schemas/education/school.schema';
import mongoose, { Document, Schema } from 'mongoose';

export interface ISubject extends Omit<CreateSubjectInput, 'board' | 'class'> {
  board: mongoose.Types.ObjectId;
  class: mongoose.Types.ObjectId;
}

export interface SubjectDocument extends ISubject, Document {
  createdAt: Date;
  updatedAt: Date;
}

const subjectSchema = new Schema<SubjectDocument>(
  {
    name: {
      type: String,
      required: [true, 'Subject name is required'],
      trim: true,
    },
    board: {
      type: Schema.Types.ObjectId,
      ref: 'Board',
      required: [true, 'Board is required'],
    },
    class: {
      type: Schema.Types.ObjectId,
      ref: 'Class',
      required: [true, 'Class is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
subjectSchema.index({ board: 1, class: 1, name: 1 }, { unique: true });

const Subject = mongoose.models.Subject || mongoose.model<SubjectDocument>('Subject', subjectSchema);

export default Subject;
