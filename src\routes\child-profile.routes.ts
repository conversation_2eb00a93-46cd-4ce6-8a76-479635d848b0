import express from 'express';
import {
  createChildProfile,
  getChildProfiles,
  getChildProfileById,
  updateChildProfile,
  deleteChildProfile,
} from '@/controllers/child-profile.controller';
import { authenticatedUser } from '@/middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use(authenticatedUser);

// Routes
router.route('/').post(createChildProfile).get(getChildProfiles);
router.route('/:id').get(getChildProfileById).patch(updateChildProfile).delete(deleteChildProfile);

export default router;
