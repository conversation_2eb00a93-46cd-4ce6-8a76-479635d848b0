import { createSessionCookie, createHash, createSessionUser, validateSession, createToken } from './auth.utils';
import { createStaffSessionCookie, createStaffSessionUser, validateStaffSession, createStaffToken } from './staff-auth.utils';
import { updateOrCreateUserSession, updateOrCreateStaffSession } from './session.utils';
import colorizer from './colorizer.utils';
import { sendVerificationEmail, sendResetPasswordEmail } from './emails.utils';
import { generateRandomString, hashString, generateSessionId, generateVerificationToken, generateResetToken } from './crypto.utils';
import { generateOTP, sendOTP, formatPhoneNumber } from './otp.utils';
import { validateData } from './validation.utils';
import { updateAccountStatus, isProfileComplete } from './account-status.utils';

import {
  formatDate,
  parseDate,
  getCurrentDate,
  getCurrentTime,
  getCurrentDateTime,
  formatDateForDisplay,
  formatRelativeTime,
  getStartOfDay,
  getEndOfDay,
  addDaysToDate,
  addMonthsToDate,
  addYearsToDate,
  calculateAge,
  calculateDetailedAge,
  isDateInPast,
  isDateInFuture,
  getDateRange,
  formatDateForFilename,
  generateTimestamp,
} from './date.utils';

export {
  // Auth utilities
  createSessionCookie,
  createSessionUser,
  validateSession,
  createToken,

  // Staff Auth utilities
  createStaffSessionCookie,
  createStaffSessionUser,
  validateStaffSession,
  createStaffToken,

  // Session utilities
  updateOrCreateUserSession,
  updateOrCreateStaffSession,

  // Crypto utilities
  createHash,
  generateRandomString,
  hashString,
  generateSessionId,
  generateVerificationToken,
  generateResetToken,

  // Email utilities
  sendVerificationEmail,
  sendResetPasswordEmail,

  // Logging utilities
  colorizer,

  // OTP utilities
  generateOTP,
  sendOTP,
  formatPhoneNumber,

  // Validation utilities
  validateData,

  // Account status utilities
  updateAccountStatus,
  isProfileComplete,

  // Date utilities
  formatDate,
  parseDate,
  getCurrentDate,
  getCurrentTime,
  getCurrentDateTime,
  formatDateForDisplay,
  formatRelativeTime,
  getStartOfDay,
  getEndOfDay,
  addDaysToDate,
  addMonthsToDate,
  addYearsToDate,
  calculateAge,
  calculateDetailedAge,
  isDateInPast,
  isDateInFuture,
  getDateRange,
  formatDateForFilename,
  generateTimestamp,
};
