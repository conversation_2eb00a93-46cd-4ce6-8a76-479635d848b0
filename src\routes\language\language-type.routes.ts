import express from 'express';
import {
  createLanguageType,
  getAllLanguageTypes,
  getLanguageTypeById,
  updateLanguageType,
  deleteLanguageType,
} from '@/controllers/language/language-type.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllLanguageTypes);
router.get('/:id', getLanguageTypeById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createLanguageType);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateLanguageType);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteLanguageType);

export default router;
