import express from 'express';
const router = express.Router();

import {
  getAllUsers,
  getSingleUser,
  showCurrentUser,
  updateUserPassword,
  updateUserAccountStatus,
  updateProfilePicture,
  getGeneralInfo,
  updateGeneralInfo,
} from '@/controllers/user.controller';
import { authenticatedUser, authorizePermissions } from '@/middleware/auth.middleware';

router.use(authenticatedUser);

router.route('/').get(authorizePermissions('super_admin', 'admin'), getAllUsers);
router.route('/showMe').get(showCurrentUser);
router.route('/updateUserPassword').patch(updateUserPassword);

router.route('/profile-picture').patch(updateProfilePicture);
router.route('/general-info').get(getGeneralInfo).patch(updateGeneralInfo);

router.route('/:userId').get(getSingleUser);
router.route('/:userId/account-status').patch(authorizePermissions('super_admin', 'admin'), updateUserAccountStatus);

export default router;
