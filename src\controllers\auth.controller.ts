import { Request, Response } from 'express';
import { generateResetToken } from '@/utils/crypto.utils';
import User from '@/models/user.model';
import Session from '@/models/session.model';
import { StatusCodes } from '@/constants';
import { BadRequestError, UnauthenticatedError, NotFoundError } from '@/errors';
import {
  createSessionCookie,
  createSessionUser,
  createHash,
  sendResetPasswordEmail,
  validateData,
  createToken,
  updateAccountStatus,
  generateOTP,
  sendOTP,
  formatPhoneNumber,
  updateOrCreateUserSession,
  validateSession,
} from '@/utils';
import { AuthenticatedRequest } from '@/types/express';
import { getOriginConfig } from '@/utils/config.utils';
import { accountStatusMap } from '@/validation/schemas/maps';
import {
  forgotPasswordSchema,
  LoginInput,
  loginSchema,
  RegisterInput,
  registerSchema,
  ResetPasswordInput,
  resetPasswordSchema,
  VerifyOtpInput,
  verifyOtpSchema,
  RequestOtpInput,
  requestOtpSchema,
} from '@/validation/schemas/auth.schema';

export const requestOTP = async (req: Request<{}, {}, RequestOtpInput>, res: Response): Promise<void> => {
  const validatedData = await validateData(requestOtpSchema, req.body);
  const { phone, userType } = validatedData;

  const formattedPhone = formatPhoneNumber(phone);

  const existingUser = await User.findOne({ phone: formattedPhone, userType });

  if (!existingUser) {
    throw new NotFoundError(`No ${userType} found with this phone number. Please register first.`);
  }

  if (existingUser && existingUser.otp?.lastAttempt) {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    if (existingUser.otp.lastAttempt > fiveMinutesAgo && existingUser.otp.requestCount && existingUser.otp.requestCount >= 3) {
      throw new BadRequestError('Too many OTP requests. Please try again after 5 minutes.');
    }

    if (existingUser.otp.lastAttempt < fiveMinutesAgo) {
      existingUser.otp.requestCount = 0;
    }
  }

  const otp = generateOTP(4);
  const otpExpiry = new Date(Date.now() + 10 * 60 * 1000);
  const now = new Date();

  existingUser.otp = {
    code: otp,
    expiry: otpExpiry,
    lastAttempt: now,
    attempts: 0,
    requestCount: (existingUser.otp?.requestCount || 0) + 1,
  };

  await existingUser.save();
  await sendOTP(formattedPhone, otp);

  // TODO: REMOVE OTP SENDING IN RESPONSE AFTER SMS INTEGRATION IS COMPLETE
  res.status(StatusCodes.OK).json({
    success: true,
    message: `OTP sent successfully to your phone number ${otp}`,
    data: { phone: formattedPhone, userType },
  });
};

export const verifyOTP = async (req: Request<{}, {}, VerifyOtpInput>, res: Response): Promise<void> => {
  const validatedData = await validateData(verifyOtpSchema, req.body);
  const { phone, otp, userType } = validatedData;

  const formattedPhone = formatPhoneNumber(phone);

  const user = await User.findOne({ phone: formattedPhone, userType }).select('+otp.code');

  if (!user) {
    throw new NotFoundError(`No ${userType} found with this phone number`);
  }

  if (!user.otp?.code || !user.otp.expiry) {
    throw new BadRequestError('No OTP was requested for this number or OTP has expired');
  }

  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

  if (!user.otp.attempts) user.otp.attempts = 0;
  if (!user.otp.lastAttempt) user.otp.lastAttempt = new Date();

  if (user.otp.lastAttempt < fiveMinutesAgo) {
    user.otp.attempts = 0;
  }

  user.otp.attempts += 1;
  user.otp.lastAttempt = now;

  if (user.otp.attempts > 3) {
    await user.save();
    throw new BadRequestError('Too many failed attempts. Please request a new OTP.');
  }

  if (user.otp.expiry < now) {
    await user.save();
    throw new BadRequestError('OTP has expired. Please request a new one');
  }

  if (user.otp.code !== otp) {
    await user.save();
    throw new UnauthenticatedError('Invalid OTP');
  }

  user.verification.isVerified = true;
  user.verification.verifiedAt = new Date();

  await updateAccountStatus(user, { isVerified: true });

  user.otp = undefined;

  await user.save();

  const sessionUser = await createSessionUser(user);

  const { sessionId } = await updateOrCreateUserSession(user._id, req);

  const token = await createSessionCookie({ res, user: sessionUser, sessionId });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'OTP verified successfully. You are now logged in.',
    data: { user: sessionUser, token },
  });
};

export const register = async (req: Request<{}, {}, RegisterInput>, res: Response): Promise<void> => {
  const validatedData = await validateData(registerSchema, req.body);

  console.log(validatedData);

  const { phone, userType } = validatedData;

  const formattedPhone = formatPhoneNumber(phone);

  const existingUser = await User.findOne({ phone: formattedPhone, userType });
  if (existingUser) {
    throw new BadRequestError(`A ${userType} with this phone number already exists`);
  }

  const otp = generateOTP(4);
  const otpExpiry = new Date(Date.now() + 10 * 60 * 1000);
  const now = new Date();

  await User.create({
    ...validatedData,
    phone: formattedPhone,
    primaryWhatsApp: formattedPhone,
    accountStatus: accountStatusMap.pending.key,
    verification: { isVerified: false },
    otp: { code: otp, expiry: otpExpiry, lastAttempt: now, attempts: 0, requestCount: 1 },
  });

  await sendOTP(formattedPhone, otp);

  // TODO: REMOVE OTP SENDING IN RESPONSE AFTER SMS INTEGRATION IS COMPLETE
  res.status(StatusCodes.CREATED).json({
    success: true,
    message: `Registration successful! OTP sent to your phone number ${otp}`,
    data: { phone: formattedPhone, userType },
  });
};

export const login = async (req: Request<{}, {}, LoginInput>, res: Response): Promise<void> => {
  const validatedData = await validateData(loginSchema, req.body);

  const query = validatedData.email
    ? { email: validatedData.email, userType: validatedData.userType }
    : { phone: formatPhoneNumber(validatedData.phone!), userType: validatedData.userType };

  const user = await User.findOne(query).select('+password');

  if (!user) throw new UnauthenticatedError('Invalid Credentials');

  const isMatch = await user.comparePassword(validatedData.password);
  if (!isMatch) throw new UnauthenticatedError('Invalid Credentials');

  if (user.accountStatus === accountStatusMap.blocked.key) throw new UnauthenticatedError('Your account has been blocked. Please contact support.');

  if (user.accountStatus === accountStatusMap.hold.key) throw new UnauthenticatedError('Your account is on hold. Please contact support.');

  if (!user.verification?.isVerified) throw new UnauthenticatedError('Please login with OTP once to verify your account.');

  const sessionUser = await createSessionUser(user);

  const { sessionId } = await updateOrCreateUserSession(user._id, req);

  const token = await createSessionCookie({ res, user: sessionUser, sessionId });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'You are successfully logged in.',
    data: { user: sessionUser, token },
  });
};

export const logout = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const sessionId = req.sessionId;

  if (sessionId) {
    await Session.updateOne(
      {
        sessionId,
        user: userId,
        userType: 'user',
        isValid: true,
      },
      { isValid: false }
    );
  } else {
    await Session.updateMany(
      {
        user: userId,
        userType: 'user',
        isValid: true,
      },
      { isValid: false }
    );
  }

  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    signed: true,
    sameSite: process.env.NODE_ENV === 'production' ? ('strict' as const) : ('lax' as const),
    path: '/',
    maxAge: 0,
  };

  if (process.env.NODE_ENV === 'production') {
    const domain = process.env.APP_DOMAIN || '.perfecttutor.site';
    Object.assign(cookieOptions, { domain });
  }

  res.cookie('session', 'logout', cookieOptions);

  res.status(StatusCodes.OK).json({ success: true, message: 'Logged out successfully!' });
};

export const forgotPassword = async (req: Request, res: Response): Promise<void> => {
  const validatedData = await validateData(forgotPasswordSchema, req.body);

  const { email, userType } = validatedData;

  const { origin } = getOriginConfig();

  const user = await User.findOne({ email, userType });

  if (user) {
    const resetPasswordToken = generateResetToken();

    const tenMinutes = new Date(Date.now() + 10 * 60 * 1000);
    user.passwordReset.token = createHash(resetPasswordToken);
    user.passwordReset.expiry = tenMinutes;

    await user.save();

    sendResetPasswordEmail({ fullName: user.fullName, email: user.email, resetPasswordToken, origin });
  }

  res.status(StatusCodes.OK).json({ success: true, message: 'If we found your email, a link to reset your password has been sent.' });
};

export const resetPassword = async (req: Request<{}, {}, ResetPasswordInput>, res: Response): Promise<void> => {
  const validatedData = await validateData(resetPasswordSchema, req.body);

  const { newPassword, confirmPassword, userType } = validatedData;
  const { token, email } = req.query as { token: string; email: string };

  if (newPassword !== confirmPassword) throw new UnauthenticatedError('Passwords should match');

  const user = await User.findOne({ email, userType }).select('+passwordReset.token +passwordReset.expiry');
  if (!user) throw new NotFoundError('No user found with the given details');

  const currentDate = new Date();

  if (user.passwordReset.token !== createHash(token)) throw new UnauthenticatedError('Token is invalid');

  if (!user.passwordReset.expiry || user.passwordReset.expiry < currentDate) throw new UnauthenticatedError('Token expired');

  user.password = newPassword;
  user.passwordReset.token = undefined;
  user.passwordReset.expiry = undefined;
  await user.save();

  res.status(StatusCodes.OK).json({ success: true, message: 'Password reset successful. You can now log in with your new password.' });
};

export const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const cookieToken = req.signedCookies.session;

    if (!cookieToken) {
      throw new UnauthenticatedError('No refresh token provided');
    }

    let payload;
    try {
      payload = await validateSession(cookieToken);
    } catch (error) {
      throw new UnauthenticatedError('Invalid or expired refresh token');
    }

    if (!payload.user || !payload.sessionId) {
      throw new UnauthenticatedError('Invalid token payload');
    }

    const userId = payload.user.userId;
    const sessionId = payload.sessionId;

    const session = await Session.findOne({ sessionId, user: userId, userType: 'user', isValid: true });

    if (!session) {
      throw new UnauthenticatedError('Session not found or invalidated');
    }

    if (session.expiresAt < new Date()) {
      throw new UnauthenticatedError('Session has expired');
    }

    const user = await User.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const sessionUser = await createSessionUser(user);

    const sessionLifetime = Number.parseInt(process.env.SESSION_MAX_AGE!, 10) || 604800000; // 7 days in ms
    const expiresAt = new Date(Date.now() + sessionLifetime);
    session.expiresAt = expiresAt;
    await session.save();

    const sessionLifetimeStr = process.env.SESSION_LIFETIME || '7d';
    const newToken = createToken({
      payload: { user: sessionUser, sessionId },
      expiresIn: sessionLifetimeStr,
    });

    const sessionMaxAge = Number.parseInt(process.env.SESSION_MAX_AGE!, 10) || 604800000;
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      signed: true,
      sameSite: process.env.NODE_ENV === 'production' ? 'strict' : ('lax' as 'strict' | 'lax' | 'none'),
      path: '/',
      maxAge: sessionMaxAge,
    };

    if (process.env.NODE_ENV === 'production') {
      const domain = process.env.APP_DOMAIN || '.perfecttutor.site';
      Object.assign(cookieOptions, { domain, expires: new Date(Date.now() + sessionMaxAge) });
    }

    res.cookie('session', newToken, cookieOptions);

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        user: sessionUser,
      },
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};
