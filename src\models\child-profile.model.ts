import { genderMap, IGenderMap } from '@/validation/schemas/maps';
import { CreateChildProfileInput } from '@/validation/schemas/parent/child-profile.schema';
import mongoose, { Document, Schema } from 'mongoose';

export interface IChildProfile extends CreateChildProfileInput {
  userId: mongoose.Types.ObjectId;
  isActive?: boolean;
}

export interface ChildProfileDocument extends IChildProfile, Document {
  createdAt: Date;
  updatedAt: Date;
}

const childProfileSchema = new Schema<ChildProfileDocument>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    fullName: {
      type: String,
      required: [true, 'Full name is required'],
      trim: true,
    },
    gender: {
      type: String,
      enum: Object.keys(genderMap),
      required: [true, 'Gender is required'],
    },
    dateOfBirth: {
      type: Date,
      required: [true, 'Date of birth is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    avatar: {
      type: String,
      default: '',
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
childProfileSchema.index({ userId: 1 });
childProfileSchema.index({ isActive: 1 });

const ChildProfile = mongoose.models.ChildProfile || mongoose.model<ChildProfileDocument>('ChildProfile', childProfileSchema);

export default ChildProfile;
