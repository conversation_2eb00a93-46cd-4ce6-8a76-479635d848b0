import express from 'express';
import serviceCategoryRoutes from './service-category.routes';

// School routes
import boardRoutes from './school/board.routes';
import classRoutes from './school/class.routes';
import subjectRoutes from './school/subject.routes';

// College routes
import streamRoutes from './college/stream.routes';
import degreeLevelRoutes from './college/degree-level.routes';
import degreeRoutes from './college/degree.routes';
import branchRoutes from './college/branch.routes';
import collegeSubjectRoutes from './college/college-subject.routes';

// Hobby routes
import hobbyTypeRoutes from './hobby/hobby-type.routes';
import hobbyRoutes from './hobby/hobby.routes';

// Language routes
import languageTypeRoutes from './language/language-type.routes';
import languageRoutes from './language/language.routes';

// Course routes
import courseTypeRoutes from './course/course-type.routes';
import courseRoutes from './course/course.routes';

// Exam routes
import examCategoryRoutes from './exam/exam-category.routes';
import examRoutes from './exam/exam.routes';
import examSubjectRoutes from './exam/exam-subject.routes';

const router = express.Router();

// Mount all education-related routes
router.use('/service-categories', serviceCategoryRoutes);

// School routes
router.use('/boards', boardRoutes);
router.use('/classes', classRoutes);
router.use('/subjects', subjectRoutes);

// College routes
router.use('/streams', streamRoutes);
router.use('/degree-levels', degreeLevelRoutes);
router.use('/degrees', degreeRoutes);
router.use('/branches', branchRoutes);
router.use('/college-subjects', collegeSubjectRoutes);

// Hobby routes
router.use('/hobby-types', hobbyTypeRoutes);
router.use('/hobbies', hobbyRoutes);

// Language routes
router.use('/language-types', languageTypeRoutes);
router.use('/languages', languageRoutes);

// Course routes
router.use('/course-types', courseTypeRoutes);
router.use('/courses', courseRoutes);

// Exam routes
router.use('/exam-categories', examCategoryRoutes);
router.use('/exams', examRoutes);
router.use('/exam-subjects', examSubjectRoutes);

export default router;
