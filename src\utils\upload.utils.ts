import path from 'path';
import fs from 'fs';
import { promisify } from 'util';
import { UploadedFile } from 'express-fileupload';
import slugify from 'slugify';

type FileType = 'image' | 'document' | 'audio' | 'video' | 'image/pdf';

interface FileUploaderOptions {
  directory: string;
  allowedFileTypes: string[];
  maxAllowedSizeInKB: number;
}

interface FileUploadParams {
  directory: string;
  file: UploadedFile;
  fileName: string;
  allowedFileTypes?: string[];
  maxAllowedSizeInKB?: number;
}

class FileUploader {
  private directory: string;
  private allowedFileTypes: string[];
  private maxAllowedSizeInKB: number;

  constructor({ directory, allowedFileTypes, maxAllowedSizeInKB }: FileUploaderOptions) {
    this.directory = directory;
    this.allowedFileTypes = allowedFileTypes;
    this.maxAllowedSizeInKB = maxAllowedSizeInKB;
  }

  private async validateFile(file: UploadedFile): Promise<void> {
    const isValidType = this.allowedFileTypes.some((type) => file.mimetype.startsWith(type.toLowerCase()));
    if (!isValidType) {
      throw new Error(`Invalid file type. Allowed types: ${this.allowedFileTypes.join(', ')}`);
    }

    const fileSizeInKB = file.size / 1024;
    if (fileSizeInKB > this.maxAllowedSizeInKB) {
      throw new Error(`File size exceeds the maximum limit of ${this.maxAllowedSizeInKB} KB.`);
    }
  }

  private async createUploadDirectory(directory: string): Promise<string> {
    const relativeUploadDir = path.join('uploads', this.directory, directory);
    const absoluteUploadDir = path.join(__dirname, '..', '..', 'public', relativeUploadDir);

    try {
      await promisify(fs.mkdir)(absoluteUploadDir, { recursive: true });
      return absoluteUploadDir;
    } catch (err) {
      console.error('Error creating upload directory:', err);
      throw new Error('Failed to create upload directory');
    }
  }

  private generateSEOFriendlyName(originalName: string): string {
    const nameWithoutExtension = path.parse(originalName).name;
    const slugifiedName = slugify(nameWithoutExtension, {
      lower: true,
      strict: true,
      trim: true,
    });
    return slugifiedName;
  }

  public async uploadFile({ file, fileName, directory }: { file: UploadedFile; fileName: string; directory: string }): Promise<string> {
    await this.validateFile(file);

    const absoluteUploadDir = await this.createUploadDirectory(directory);

    const seoFriendlyName = this.generateSEOFriendlyName(fileName);
    const originalExtension = path.extname(file.name);
    const originalFileName = `${seoFriendlyName}${originalExtension}`;
    const originalFilePath = path.join(absoluteUploadDir, originalFileName);

    await promisify(fs.writeFile)(originalFilePath, file.data);

    const publicFilePath = path.posix.join('/', 'uploads', this.directory, directory, originalFileName);

    return publicFilePath;
  }
}

function createFileUploader(type: FileType): FileUploader {
  switch (type) {
    case 'image':
      return new FileUploader({
        directory: 'images',
        allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp', 'image/svg+xml', 'image/avif'],
        maxAllowedSizeInKB: 5 * 1024, // 5MB
      });
    case 'document':
      return new FileUploader({
        directory: 'documents',
        allowedFileTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        maxAllowedSizeInKB: 10 * 1024, // 10MB
      });
    case 'audio':
      return new FileUploader({
        directory: 'audio',
        allowedFileTypes: ['audio/mpeg', 'audio/wav', 'audio/ogg'],
        maxAllowedSizeInKB: 20 * 1024, // 20MB
      });
    case 'video':
      return new FileUploader({
        directory: 'videos',
        allowedFileTypes: ['video/mp4', 'video/webm', 'video/ogg'],
        maxAllowedSizeInKB: 50 * 1024, // 50MB
      });
    case 'image/pdf':
      return new FileUploader({
        directory: 'images',
        allowedFileTypes: [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/bmp',
          'image/tiff',
          'image/webp',
          'image/svg+xml',
          'image/avif',
          'application/pdf',
        ],
        maxAllowedSizeInKB: 5 * 1024, // 5MB
      });
    default:
      throw new Error(`Unsupported file uploader type: ${type}`);
  }
}

async function handleFileUpload({
  directory,
  file,
  fileName,
  allowedFileTypes = ['image'],
  maxAllowedSizeInKB = 1024,
}: FileUploadParams): Promise<string> {
  const fileTypes = Array.isArray(allowedFileTypes) ? allowedFileTypes : [allowedFileTypes];

  const uploader = new FileUploader({ directory, allowedFileTypes: fileTypes, maxAllowedSizeInKB });

  return await uploader.uploadFile({ file, fileName, directory });
}

export { handleFileUpload, createFileUploader, FileUploader };
