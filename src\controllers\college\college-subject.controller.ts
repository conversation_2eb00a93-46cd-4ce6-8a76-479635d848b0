import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import CollegeSubject from '@/models/college/college-subject.model';
import Branch from '@/models/college/branch.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createCollegeSubjectSchema, UpdateCollegeSubjectInput } from '@/validation/schemas/education/college.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const collegeSubjectAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'branches',
      localField: 'branch',
      foreignField: '_id',
      as: 'branchDetails',
    },
  },
  {
    $addFields: {
      branchDetails: { $arrayElemAt: ['$branchDetails', 0] },
    },
  },
  {
    $lookup: {
      from: 'degrees',
      localField: 'branchDetails.degree',
      foreignField: '_id',
      as: 'degreeDetails',
    },
  },
  {
    $addFields: {
      degreeDetails: { $arrayElemAt: ['$degreeDetails', 0] },
    },
  },
  { $sort: { name: 1 } },
];

/**
 * Create a new college subject
 * @route POST /api/v1/education/college-subjects
 * @access Private (Admin)
 */
export const createCollegeSubject = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createCollegeSubjectSchema, req.body);
  const { name, branch } = validatedData;

  if (!isValidObjectId(branch)) {
    throw new BadRequestError('Please provide a valid branch ID');
  }

  const branchExists = await Branch.findById(branch);
  if (!branchExists) {
    throw new NotFoundError(`No branch found with id: ${branch}`);
  }

  // Check if a subject with the same name and branch already exists
  const existingSubject = await CollegeSubject.findOne({ name, branch });
  if (existingSubject) {
    throw new BadRequestError(`A subject with the name '${name}' already exists for this branch`);
  }

  const subject = await CollegeSubject.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'College subject created successfully',
    data: { subject },
  });
};

/**
 * Get all college subjects
 * @route GET /api/v1/education/college-subjects
 * @access Public
 */
export const getAllCollegeSubjects = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.branch && isValidObjectId(queryString.branch)) {
    matchStage['branch'] = new mongoose.Types.ObjectId(queryString.branch as string);
    delete queryString.branch;
  }

  const baseAggregation = [{ $match: matchStage }, ...collegeSubjectAggregation];

  const queryManager = new AggregateQueryManager({
    model: CollegeSubject,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [subjects, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'College subjects fetched successfully',
    data: { subjects, pagination },
  });
};

/**
 * Get a college subject by ID
 * @route GET /api/v1/education/college-subjects/:id
 * @access Public
 */
export const getCollegeSubjectById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid college subject ID');
  }

  const [subject] = await CollegeSubject.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...collegeSubjectAggregation,
  ]);

  if (!subject) {
    throw new NotFoundError(`No college subject found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'College subject fetched successfully',
    data: { subject },
  });
};

/**
 * Update a college subject
 * @route PATCH /api/v1/education/college-subjects/:id
 * @access Private (Admin)
 */
export const updateCollegeSubject = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateCollegeSubjectInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid college subject ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const subject = await CollegeSubject.findById(id);
  if (!subject) {
    throw new NotFoundError(`No college subject found with id: ${id}`);
  }

  // Validate branch if provided
  if (updateData.branch && !isValidObjectId(updateData.branch)) {
    throw new BadRequestError('Please provide a valid branch ID');
  }

  if (updateData.branch) {
    const branchExists = await Branch.findById(updateData.branch);
    if (!branchExists) {
      throw new NotFoundError(`No branch found with id: ${updateData.branch}`);
    }
  }

  // Check for duplicate if name or branch is being updated
  if (updateData.name || updateData.branch) {
    const name = updateData.name || subject.name;
    const branch = updateData.branch || subject.branch;

    const existingSubject = await CollegeSubject.findOne({
      name,
      branch,
      _id: { $ne: id },
    });

    if (existingSubject) {
      throw new BadRequestError(`A subject with the name '${name}' already exists for this branch`);
    }
  }

  await CollegeSubject.findByIdAndUpdate(id, updateData, { runValidators: true });

  const [updatedSubject] = await CollegeSubject.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...collegeSubjectAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'College subject updated successfully',
    data: { subject: updatedSubject },
  });
};

/**
 * Delete a college subject
 * @route DELETE /api/v1/education/college-subjects/:id
 * @access Private (Super Admin)
 */
export const deleteCollegeSubject = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid college subject ID');
  }

  const subject = await CollegeSubject.findById(id);
  if (!subject) {
    throw new NotFoundError(`No college subject found with id: ${id}`);
  }

  await CollegeSubject.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'College subject deleted successfully',
    data: null,
  });
};
