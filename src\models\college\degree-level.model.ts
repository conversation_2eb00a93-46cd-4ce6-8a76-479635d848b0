import mongoose, { Document, Schema } from 'mongoose';
import { CreateDegreeLevelInput } from '@/validation/schemas/education/college.schema';

export interface IDegreeLevel extends Omit<CreateDegreeLevelInput, 'stream'> {
  stream: mongoose.Types.ObjectId;
}

export interface DegreeLevelDocument extends IDegreeLevel, Document {
  createdAt: Date;
  updatedAt: Date;
}

const degreeLevelSchema = new Schema<DegreeLevelDocument>(
  {
    name: {
      type: String,
      required: [true, 'Degree level name is required'],
      trim: true,
    },
    stream: {
      type: Schema.Types.ObjectId,
      ref: 'Stream',
      required: [true, 'Stream is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
degreeLevelSchema.index({ stream: 1, name: 1 }, { unique: true });

const DegreeLevel = mongoose.models.DegreeLevel || mongoose.model<DegreeLevelDocument>('DegreeLevel', degreeLevelSchema);

export default DegreeLevel;
