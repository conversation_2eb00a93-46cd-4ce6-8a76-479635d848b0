# Search Examples

## School

Fetch: Class and Board

**Example 1:**

- Search: `class 1`
- Result: Class 1 - CBSE, Class 1 - IB, Class 1 - ICSE
- Note: The search returns all classes matching "Class 1" across all boards, accommodating variations in case sensitivity and notation formats (e.g., "Class 1", "Class I").

**Example 2:**

- Search: `CBSE`
- Result: CBSE - Class 1, CBSE - Class 2, CBSE - Class 3, ...
- Note: The search returns all classes matching "CBSE" across all classes, accommodating variations in case sensitivity and notation formats (e.g., "CBSE", "CBSE Board").

---

## College

Fetch: Degree and Branch

**Example 1:**

- Search: `BTech`
- Result: BTech - Mechanical, BTech - Chemical, BTech - Electrical
- Note: The search returns all degrees matching "BTech" across all branches, accommodating variations in case sensitivity and notation formats (e.g., "BTech", "B.Tech"). The search algorithm ignores special characters like periods to handle common variations in degree notation (e.g., "B.Sc", "BSc").

**Example 2:**

- Search: `Mechanical`
- Result: BTech - Mechanical, BE - Mechanical
- Note: The search returns all branches matching "Mechanical" branch across all degrees, accommodating variations in case sensitivity.

---

## Hobbies

Fetch: <PERSON><PERSON> and <PERSON><PERSON> Type

**Example 1:**

- Search: `Dance`
- Result: Dance - Kathakali, Dance - Bharatanatyam
- Note: The search returns all hobbies matching "Dance" across all hobby types, accommodating variations in case sensitivity and notation formats (e.g., "Dance", "Dance Hobby").

**Example 2:**

- Search: `Kathakali`
- Result: Dance - Kathakali
- Note: The search returns all hobbies matching "Kathakali" across all hobby types, accommodating variations in case sensitivity and notation formats (e.g., "Kathakali", "Kathakali Hobby").

---

## Language

Fetch: Language Family and Language Name

**Example 1:**

- Search: `French`
- Result: Foreign Language - French
- Note: The search returns all languages matching "French" across all language families, accommodating variations in case sensitivity and notation formats (e.g., "French", "French Language").

**Example 2:**

- Search: `Hindi`
- Result: Indian Language - Hindi
- Note: The search returns all languages matching "Hindi" across all language families, accommodating variations in case sensitivity and notation formats (e.g., "Hindi", "Hindi Language").

---

## Exam

Fetch: Exam Level and Exam Name

**Example 1:**

- Search: `Entrance Exam`
- Result: Entrance Exam - NEET, Entrance Exam - JEE, Entrance Exam - Mains
- Note: The search returns all exams matching "Entrance Exam" across all exam levels, accommodating variations in case sensitivity and notation formats (e.g., "Entrance Exam", "Entrance Exams").

**Example 2:**

- Search: `NEET`
- Result: Entrance Exam - NEET
- Note: The search returns all exams matching "NEET" across all exam levels, accommodating variations in case sensitivity and notation formats (e.g., "NEET", "NEET Exam").
