import express from 'express';
import {
  createBoard,
  getAllBoards,
  getBoardById,
  updateBoard,
  deleteBoard,
} from '@/controllers/school/board.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllBoards);
router.get('/:id', getBoardById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createBoard);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateBoard);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteBoard);

export default router;
