import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Exam from '@/models/exam/exam.model';
import ExamCategory from '@/models/exam/exam-category.model';
import ExamSubject from '@/models/exam/exam-subject.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createExamSchema, UpdateExamInput } from '@/validation/schemas/education/exam.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const examAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'examcategories',
      localField: 'examCategory',
      foreignField: '_id',
      as: 'examCategoryDetails',
    },
  },
  { $unwind: { path: '$examCategoryDetails', preserveNullAndEmptyArrays: true } },
  { $sort: { 'examCategoryDetails.name': 1, name: 1 } },
];

/**
 * Create a new exam
 * @route POST /api/v1/education/exams
 * @access Admin only
 */
export const createExam = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createExamSchema, req.body);
  const { name, examCategory } = validatedData;

  if (!isValidObjectId(examCategory)) {
    throw new BadRequestError('Please provide a valid exam category ID');
  }

  const examCategoryExists = await ExamCategory.findById(examCategory);
  if (!examCategoryExists) {
    throw new NotFoundError(`No exam category found with id: ${examCategory}`);
  }

  const existingExam = await Exam.findOne({ name, examCategory });
  if (existingExam) {
    throw new BadRequestError(`An exam with the name '${name}' already exists for this exam category`);
  }

  const exam = await Exam.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Exam created successfully',
    data: { exam },
  });
};

/**
 * Get all exams
 * @route GET /api/v1/education/exams
 * @access Public
 */
export const getAllExams = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  if (queryString.examCategory && isValidObjectId(queryString.examCategory)) {
    matchStage['examCategory'] = new mongoose.Types.ObjectId(queryString.examCategory as string);
    delete queryString.examCategory;
  }

  const baseAggregation = [{ $match: matchStage }, ...examAggregation];

  const queryManager = new AggregateQueryManager({
    model: Exam,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [exams, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exams fetched successfully',
    data: { exams, pagination },
  });
};

/**
 * Get exams by exam category
 * @route GET /api/v1/education/exams/category/:examCategoryId
 * @access Public
 */
export const getExamsByCategory = async (req: Request, res: Response): Promise<void> => {
  const { examCategoryId } = req.params;
  const queryString = req.query;

  if (!isValidObjectId(examCategoryId)) {
    throw new BadRequestError('Please provide a valid exam category ID');
  }

  const examCategoryExists = await ExamCategory.findById(examCategoryId);
  if (!examCategoryExists) {
    throw new NotFoundError(`No exam category found with id: ${examCategoryId}`);
  }

  const matchStage = { examCategory: new mongoose.Types.ObjectId(examCategoryId) };
  const baseAggregation = [{ $match: matchStage }, ...examAggregation];

  const queryManager = new AggregateQueryManager({
    model: Exam,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [exams, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exams fetched successfully',
    data: { exams, pagination, examCategory: examCategoryExists },
  });
};

/**
 * Get a specific exam by ID
 * @route GET /api/v1/education/exams/:id
 * @access Public
 */
export const getExamById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam ID');
  }

  const [exam] = await Exam.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...examAggregation]);

  if (!exam) {
    throw new NotFoundError(`No exam found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam fetched successfully',
    data: { exam },
  });
};

/**
 * Update an exam
 * @route PATCH /api/v1/education/exams/:id
 * @access Admin only
 */
export const updateExam = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const updateData = req.body as UpdateExamInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam ID');
  }

  if (Object.keys(updateData).length === 0) {
    throw new BadRequestError('Please provide data to update');
  }

  const exam = await Exam.findById(id);
  if (!exam) {
    throw new NotFoundError(`No exam found with id: ${id}`);
  }

  // Validate exam category if provided
  if (updateData.examCategory && !isValidObjectId(updateData.examCategory)) {
    throw new BadRequestError('Please provide a valid exam category ID');
  }

  if (updateData.examCategory) {
    const examCategoryExists = await ExamCategory.findById(updateData.examCategory);
    if (!examCategoryExists) {
      throw new NotFoundError(`No exam category found with id: ${updateData.examCategory}`);
    }
  }

  // Validate the update data
  const validatedData = await validateData(createExamSchema.partial(), updateData);

  // Check for duplicate name within the same exam category
  if (validatedData.name || validatedData.examCategory) {
    const examCategoryId = validatedData.examCategory || exam.examCategory;
    const examName = validatedData.name || exam.name;

    const existingExam = await Exam.findOne({
      name: examName,
      examCategory: examCategoryId,
      _id: { $ne: id },
    });

    if (existingExam) {
      throw new BadRequestError(`An exam with the name '${examName}' already exists for this exam category`);
    }
  }

  await Exam.findByIdAndUpdate(id, validatedData, { runValidators: true });

  // Get the updated exam with aggregation
  const [updatedExam] = await Exam.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...examAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam updated successfully',
    data: { exam: updatedExam },
  });
};

/**
 * Delete an exam
 * @route DELETE /api/v1/education/exams/:id
 * @access Super Admin only
 */
export const deleteExam = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid exam ID');
  }

  const exam = await Exam.findById(id);
  if (!exam) {
    throw new NotFoundError(`No exam found with id: ${id}`);
  }

  // Check if there are any exam subjects associated with this exam
  const associatedExamSubjects = await ExamSubject.findOne({ exam: id });
  if (associatedExamSubjects) {
    throw new BadRequestError(
      'Cannot delete this exam because there are exam subjects associated with it. Please delete those exam subjects first.'
    );
  }

  await Exam.findByIdAndDelete(id);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Exam deleted successfully',
    data: null,
  });
};
