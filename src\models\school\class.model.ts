import { CreateClassInput } from '@/validation/schemas/education/school.schema';
import mongoose, { Document, Schema } from 'mongoose';

export interface IClass extends Omit<CreateClassInput, 'board'> {
  board: mongoose.Types.ObjectId;
}

export interface ClassDocument extends IClass, Document {
  createdAt: Date;
  updatedAt: Date;
}

const classSchema = new Schema<ClassDocument>(
  {
    name: {
      type: String,
      required: [true, 'Class name is required'],
      trim: true,
    },
    displayOrder: {
      type: Number,
      required: [true, 'Display order is required'],
    },
    board: {
      type: Schema.Types.ObjectId,
      ref: 'Board',
      required: [true, 'Board is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
classSchema.index({ board: 1, name: 1 }, { unique: true });
classSchema.index({ board: 1, displayOrder: 1 });

const Class = mongoose.models.Class || mongoose.model<ClassDocument>('Class', classSchema);

export default Class;
