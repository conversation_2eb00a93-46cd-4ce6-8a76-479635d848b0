import mongoose, { Document, Schema } from 'mongoose';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import {
  ITutorGenderMap,
  IStartTimeMap,
  IDeliveryModeMap,
  IEnquiryStatusMap,
  tutorGenderMap,
  startTimeMap,
  deliveryModeMap,
  enquiryStatusMap,
} from '@/validation/schemas/enquiry.maps';

export interface IEnquiry {
  user: mongoose.Types.ObjectId;
  childProfile: mongoose.Types.ObjectId;
  category: keyof typeof serviceCategoryMap;

  // School specific fields
  board?: mongoose.Types.ObjectId;
  class?: mongoose.Types.ObjectId;
  subjects?: mongoose.Types.ObjectId[];
  allSubjects?: boolean;

  // College specific fields
  stream?: mongoose.Types.ObjectId;
  degreeLevel?: mongoose.Types.ObjectId;
  degree?: mongoose.Types.ObjectId;
  branch?: mongoose.Types.ObjectId;
  collegeSubjects?: mongoose.Types.ObjectId[];

  // Hobby specific fields
  hobbyType?: mongoose.Types.ObjectId;
  hobby?: mongoose.Types.ObjectId;

  // Language specific fields
  languageType?: mongoose.Types.ObjectId;
  language?: mongoose.Types.ObjectId;

  // Course specific fields
  courseType?: mongoose.Types.ObjectId;
  course?: mongoose.Types.ObjectId;

  // Exam specific fields
  examCategory?: mongoose.Types.ObjectId;
  exam?: mongoose.Types.ObjectId;
  examSubjects?: mongoose.Types.ObjectId[];

  // Preferences
  location: {
    address: string;
    landmark?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  tutorGender: ITutorGenderMap;
  classesPerWeek: number;
  startTime: IStartTimeMap;
  deliveryModes: IDeliveryModeMap[];
  specialRequirements?: string;
  isStudentSpecial?: boolean;

  // Status
  status: IEnquiryStatusMap;
  isActive: boolean;
}

export interface EnquiryDocument extends IEnquiry, Document {
  createdAt: Date;
  updatedAt: Date;
}

const enquirySchema = new Schema<EnquiryDocument>(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User is required'],
    },
    childProfile: {
      type: Schema.Types.ObjectId,
      ref: 'ChildProfile',
      required: [true, 'Child profile is required'],
    },
    category: {
      type: String,
      enum: Object.keys(serviceCategoryMap),
      required: [true, 'Service category is required'],
    },

    // School specific fields
    board: {
      type: Schema.Types.ObjectId,
      ref: 'Board',
    },
    class: {
      type: Schema.Types.ObjectId,
      ref: 'Class',
    },
    subjects: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Subject',
      },
    ],
    allSubjects: {
      type: Boolean,
      default: false,
    },
    // College specific fields
    stream: {
      type: Schema.Types.ObjectId,
      ref: 'Stream',
    },
    degreeLevel: {
      type: Schema.Types.ObjectId,
      ref: 'DegreeLevel',
    },
    degree: {
      type: Schema.Types.ObjectId,
      ref: 'Degree',
    },
    branch: {
      type: Schema.Types.ObjectId,
      ref: 'Branch',
    },
    collegeSubjects: [
      {
        type: Schema.Types.ObjectId,
        ref: 'CollegeSubject',
      },
    ],

    // Hobby specific fields
    hobbyType: {
      type: Schema.Types.ObjectId,
      ref: 'HobbyType',
    },
    hobby: {
      type: Schema.Types.ObjectId,
      ref: 'Hobby',
    },

    // Language specific fields
    languageType: {
      type: Schema.Types.ObjectId,
      ref: 'LanguageType',
    },
    language: {
      type: Schema.Types.ObjectId,
      ref: 'Language',
    },

    // Course specific fields
    courseType: {
      type: Schema.Types.ObjectId,
      ref: 'CourseType',
    },
    course: {
      type: Schema.Types.ObjectId,
      ref: 'Course',
    },

    // Exam specific fields
    examCategory: {
      type: Schema.Types.ObjectId,
      ref: 'ExamCategory',
    },
    exam: {
      type: Schema.Types.ObjectId,
      ref: 'Exam',
    },
    examSubjects: [
      {
        type: Schema.Types.ObjectId,
        ref: 'ExamSubject',
      },
    ],

    // Preferences
    location: {
      address: {
        type: String,
        required: [true, 'Address is required'],
      },
      landmark: String,
      coordinates: {
        lat: Number,
        lng: Number,
      },
    },
    tutorGender: {
      type: String,
      enum: Object.keys(tutorGenderMap),
      default: 'any',
    },
    classesPerWeek: {
      type: Number,
      required: [true, 'Number of classes per week is required'],
      min: 1,
      max: 7,
    },
    startTime: {
      type: String,
      enum: Object.keys(startTimeMap),
      required: [true, 'Start time preference is required'],
    },
    deliveryModes: [
      {
        type: String,
        enum: Object.keys(deliveryModeMap),
      },
    ],
    specialRequirements: String,
    isStudentSpecial: {
      type: Boolean,
      default: false,
    },

    // Status
    status: {
      type: String,
      enum: Object.keys(enquiryStatusMap),
      default: 'pending',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
enquirySchema.index({ user: 1 });
enquirySchema.index({ childProfile: 1 });
enquirySchema.index({ category: 1 });
enquirySchema.index({ status: 1 });
enquirySchema.index({ isActive: 1 });
enquirySchema.index({ createdAt: -1 });

const Enquiry = mongoose.models.Enquiry || mongoose.model<EnquiryDocument>('Enquiry', enquirySchema);

export default Enquiry;
