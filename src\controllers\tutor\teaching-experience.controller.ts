import { Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import { validateData } from '@/utils';
import { AuthenticatedRequest } from '@/types/express';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import TeachingExperience from '@/models/tutor/teaching-experience.model';
import { createTeachingExperienceSchema, updateTeachingExperienceSchema } from '@/validation/schemas/tutor/profiles/teaching-experience.schema';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import { getBusinessLocationType, handleBusinessLocation } from '@/utils/business-location.utils';
import { ITuitionTypeMap } from '@/validation/schemas/tutor/tuition.maps';

const teachingExperienceAggregation: PipelineStage[] = [
  {
    $lookup: {
      from: 'businesslocations',
      localField: 'businessLocationId',
      foreignField: '_id',
      as: 'businessLocationDetails',
    },
  },
  {
    $unwind: {
      path: '$businessLocationDetails',
      preserveNullAndEmptyArrays: true,
    },
  },
  { $sort: { createdAt: -1 } },
];

interface IBusinessLocationData {
  businessLocationName: string;
  location: string;
  tuitionType: ITuitionTypeMap;
  coordinates?: { lng: number; lat: number };
}

const processTeachingBusinessLocation = async (data: IBusinessLocationData) => {
  const { businessLocationName, location, tuitionType, coordinates } = data;
  if (!businessLocationName) throw new BadRequestError('Business location name is required');

  return await handleBusinessLocation({
    name: businessLocationName,
    location,
    type: getBusinessLocationType(tuitionType),
    coordinates,
  });
};

export const getAllTeachingExperience = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user!.userId;
  const queryString = req.query;

  const matchStage = { userId: new mongoose.Types.ObjectId(userId) };
  const baseAggregation = [{ $match: matchStage }, ...teachingExperienceAggregation];

  const queryManager = new AggregateQueryManager({ model: TeachingExperience, queryString, baseAggregation }).filter().paginate();

  const [teachingExperiences, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Teaching experiences fetched successfully',
    data: { teachingExperiences, pagination },
  });
};

export const createTeachingExperience = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createTeachingExperienceSchema, req.body);
  const userId = req.user!.userId;

  let businessLocationId = undefined;
  if (validatedData.placeName && validatedData.location) {
    businessLocationId = await processTeachingBusinessLocation({
      businessLocationName: validatedData.placeName,
      location: validatedData.location,
      tuitionType: validatedData.tuitionType,
      coordinates: validatedData.coordinates,
    });
  }

  const teachingExperience = await TeachingExperience.create({ ...validatedData, userId, businessLocationId });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Teaching experience created successfully',
    data: { teachingExperience },
  });
};

export const updateTeachingExperience = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const validatedData = await validateData(updateTeachingExperienceSchema, req.body);
  const userId = req.user!.userId;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Invalid teaching experience ID');
  }

  let businessLocationId = undefined;

  if (validatedData.placeName && validatedData.location) {
    businessLocationId = await processTeachingBusinessLocation({
      businessLocationName: validatedData.placeName,
      location: validatedData.location,
      tuitionType: validatedData.tuitionType || 'other',
      coordinates: validatedData.coordinates,
    });
  }

  const teachingExperience = await TeachingExperience.findOneAndUpdate(
    { _id: id, userId },
    { ...validatedData, businessLocationId },
    { new: true, runValidators: true }
  );

  if (!teachingExperience) {
    throw new NotFoundError('Teaching experience not found');
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Teaching experience updated successfully',
    data: { teachingExperience },
  });
};

export const deleteTeachingExperience = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const { id } = req.params;
  const userId = req.user!.userId;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Invalid teaching experience ID');
  }

  const teachingExperience = await TeachingExperience.findOneAndDelete({ _id: id, userId });
  if (!teachingExperience) {
    throw new NotFoundError('Teaching experience not found');
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Teaching experience deleted successfully',
    data: { teachingExperience },
  });
};
