import { z } from 'zod';
import { objectIdSchema, coordinatesSchema } from '../../common.schema';
import { tuitionTypeMap, ITuitionTypeMap } from '../tuition.maps';

const commonFieldsSchema = z.object({
  tuitionType: z.enum(Object.keys(tuitionTypeMap) as [ITuitionTypeMap, ...ITuitionTypeMap[]]).describe('Type of tuition experience'),
  experienceMonths: z.number().min(0, 'Experience must be non-negative').describe('Experience duration in months'),
  location: z.string().min(1, 'Location is required').describe('Location of teaching experience'),
  coordinates: coordinatesSchema.optional().describe('Geographical coordinates of the location'),
});

const privateTuitionSchema = z.object({
  tuitionType: z.literal('private').describe('Private tuition type'),
  placeName: z.literal('Self').describe('Place name for private tuition'),
  location: z.literal('Self').describe('Location for private tuition'),
});

const institutionTuitionSchema = z.object({
  tuitionType: z.enum(['school', 'college', 'institute', 'other']).describe('Institution tuition type'),
  placeName: z.string().min(1, 'Place name is required').describe('Name of the institution'),
  businessLocationId: objectIdSchema.optional().describe('Business/Organization ID for autocomplete selection'),
});

export const createTeachingExperienceSchema = z
  .object(commonFieldsSchema.shape)
  .and(z.discriminatedUnion('tuitionType', [privateTuitionSchema, institutionTuitionSchema]))
  .superRefine((data, ctx) => {
    if (data.location.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Location cannot be empty',
        path: ['location'],
      });
    }

    if (data.tuitionType !== 'private' && data.placeName.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Place name cannot be empty',
        path: ['placeName'],
      });
    }
  });

export const updateTeachingExperienceSchema = z
  .object({
    tuitionType: z.enum(Object.keys(tuitionTypeMap) as [ITuitionTypeMap, ...ITuitionTypeMap[]]).optional(),
    experienceMonths: z.number().min(0, 'Experience must be non-negative').optional(),
    location: z.string().optional(),
    placeName: z.string().optional(),
    businessLocationId: objectIdSchema.optional(),
    coordinates: coordinatesSchema.optional(),
    isActive: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.location !== undefined && data.location.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Location cannot be empty',
        path: ['location'],
      });
    }

    if (data.tuitionType !== 'private' && data.placeName !== undefined && data.placeName.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Place name cannot be empty',
        path: ['placeName'],
      });
    }
  });

export type CreateTeachingExperienceInput = z.infer<typeof createTeachingExperienceSchema>;
export type UpdateTeachingExperienceInput = z.infer<typeof updateTeachingExperienceSchema>;
