import express from 'express';
import {
  createCourseType,
  getAllCourseTypes,
  getCourseTypeById,
  updateCourseType,
  deleteCourseType,
} from '@/controllers/course/course-type.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllCourseTypes);
router.get('/:id', getCourseTypeById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createCourseType);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateCourseType);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteCourseType);

export default router;
