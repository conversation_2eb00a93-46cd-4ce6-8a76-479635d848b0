import express from 'express';
import {
  createEducationDetail,
  getEducationDetails,
  getAllUserEducationDetails,
  getEducationDetailById,
  updateEducationDetail,
  deleteEducationDetail,
} from '@/controllers/education-detail.controller';
import { authenticatedUser } from '@/middleware/auth.middleware';

const router = express.Router();

// All routes require authentication
router.use(authenticatedUser);

// Routes
router.route('/').get(getEducationDetails).post(createEducationDetail);
router.route('/all').get(getAllUserEducationDetails); // Get all education details for logged-in user
router.route('/:id').get(getEducationDetailById).patch(updateEducationDetail).delete(deleteEducationDetail);

export default router;
