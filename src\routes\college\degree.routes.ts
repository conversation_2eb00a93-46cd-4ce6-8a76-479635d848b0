import express from 'express';
import { createDegree, getAllDegrees, getDegreeById, updateDegree, deleteDegree } from '@/controllers/college/degree.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllDegrees);
router.get('/:id', getDegreeById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createDegree);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateDegree);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteDegree);

export default router;
