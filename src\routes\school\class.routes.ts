import express from 'express';
import {
  createClass,
  getAllClasses,
  getClassesByBoard,
  getClassById,
  updateClass,
  deleteClass,
} from '@/controllers/school/class.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllClasses);
router.get('/board/:boardId', getClassesByBoard);
router.get('/:id', getClassById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createClass);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateClass);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteClass);

export default router;
