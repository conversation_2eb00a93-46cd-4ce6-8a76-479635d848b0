import mongoose, { Document, Schema } from 'mongoose';
import { CreateHobbyInput } from '@/validation/schemas/education/hobby.schema';

export interface IHobby extends Omit<CreateHobbyInput, 'hobbyType'> {
  hobbyType: mongoose.Types.ObjectId;
}

export interface HobbyDocument extends IHobby, Document {
  createdAt: Date;
  updatedAt: Date;
}

const hobbySchema = new Schema<HobbyDocument>(
  {
    name: {
      type: String,
      required: [true, 'Hobby name is required'],
      trim: true,
    },
    hobbyType: {
      type: Schema.Types.ObjectId,
      ref: 'HobbyType',
      required: [true, 'Hobby type is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
hobbySchema.index({ hobbyType: 1, name: 1 }, { unique: true });

const Hobby = mongoose.models.Hobby || mongoose.model<HobbyDocument>('Hobby', hobbySchema);

export default Hobby;
