import mongoose, { Document, Schema } from 'mongoose';

export interface ISession {
  sessionId: string;
  IP: string;
  userAgent: string;
  isValid: boolean;
  user: mongoose.Schema.Types.ObjectId;
  userType: 'user';
  expiresAt: Date;
}

interface SessionDocument extends ISession, Document {
  createdAt: Date;
  updatedAt: Date;
}

const sessionSchema = new Schema<SessionDocument>(
  {
    sessionId: { type: String, required: true, unique: true },
    IP: { type: String, required: true },
    userAgent: { type: String, required: true },
    isValid: { type: Boolean, default: true },
    user: { type: Schema.Types.ObjectId, required: true },
    userType: { type: String, enum: ['user'], required: true, default: 'user' },
    expiresAt: { type: Date, required: true },
  },
  { timestamps: true }
);

sessionSchema.index({ user: 1, isValid: 1 });
sessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

const Session = mongoose.models.Session || mongoose.model<SessionDocument>('Session', sessionSchema);

export default Session;
