import mongoose, { Document, Schema } from 'mongoose';
import { CreateExamInput } from '@/validation/schemas/education/exam.schema';

export interface IExam extends Omit<CreateExamInput, 'examCategory'> {
  examCategory: mongoose.Types.ObjectId;
}

export interface ExamDocument extends IExam, Document {
  createdAt: Date;
  updatedAt: Date;
}

const examSchema = new Schema<ExamDocument>(
  {
    name: {
      type: String,
      required: [true, 'Exam name is required'],
      trim: true,
    },
    examCategory: {
      type: Schema.Types.ObjectId,
      ref: 'ExamCategory',
      required: [true, 'Exam category is required'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
examSchema.index({ examCategory: 1, name: 1 }, { unique: true });

const Exam = mongoose.models.Exam || mongoose.model<ExamDocument>('Exam', examSchema);

export default Exam;
