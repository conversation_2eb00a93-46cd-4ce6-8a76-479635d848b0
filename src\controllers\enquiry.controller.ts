import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import { validateData } from '@/utils';
import { AuthenticatedRequest } from '@/types/express';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';

// Models
import Enquiry from '@/models/enquiry.model';
import ChildProfile from '@/models/child-profile.model';
import Class, { ClassDocument } from '@/models/school/class.model';
import Branch, { BranchDocument } from '@/models/college/branch.model';
import Hobby from '@/models/hobby/hobby.model';
import Language from '@/models/language/language.model';
import Exam, { ExamDocument } from '@/models/exam/exam.model';

// Schemas
import { searchQuerySchema, createEnquirySchema } from '@/validation/schemas/enquiry.schema';
import { IServiceCategoryMap, serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import CollegeSubject from '@/models/college/college-subject.model';
import Course from '@/models/course/course.model';
import ExamSubject from '@/models/exam/exam-subject.model';
import Subject from '@/models/school/subject.model';
import { DegreeLevelDocument } from '@/models/college/degree-level.model';
import { DegreeDocument } from '@/models/college/degree.model';
import { StreamDocument } from '@/models/college/stream.model';
import { CourseTypeDocument } from '@/models/course/course-type.model';
import { ExamCategoryDocument } from '@/models/exam/exam-category.model';
import { HobbyTypeDocument } from '@/models/hobby/hobby-type.model';
import { LanguageTypeDocument } from '@/models/language/language-type.model';
import { BoardDocument } from '@/models/school/board.model';

interface ISearchResponseItem {
  id: string;
  name: string;
}

interface ISearchResponse {
  matches: Array<{
    type: IServiceCategoryMap;
    details: {
      // School fields
      subject?: ISearchResponseItem;
      class?: ISearchResponseItem;
      board?: ISearchResponseItem;
      subjects?: ISearchResponseItem[];
      // College fields
      branch?: ISearchResponseItem;
      degree?: ISearchResponseItem;
      stream?: ISearchResponseItem;
      // Hobby fields
      hobby?: ISearchResponseItem;
      hobbyType?: ISearchResponseItem;
      // Language fields
      language?: ISearchResponseItem;
      languageType?: ISearchResponseItem;
      // Course fields
      course?: ISearchResponseItem;
      courseCategory?: ISearchResponseItem;
      // Exam fields
      exam?: ISearchResponseItem;
      examCategory?: ISearchResponseItem;
      examSubjects?: ISearchResponseItem[];
    };
    displayText: string;
  }>;
}

// EDUCATION ITEMS
export interface IEducationItem {
  _id: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Metadata interfaces

// 1. School
export interface ISchoolMetadata {
  class: ClassDocument;
  board: BoardDocument;
}

// 2. College
export interface ICollegeMetadata {
  branch: BranchDocument;
  degree: DegreeDocument;
  degreeLevel: DegreeLevelDocument;
  stream: StreamDocument;
}

// 3. Language
export interface ILanguageMetadata {
  languageType: LanguageTypeDocument;
}

// 4. Hobby
export interface IHobbyMetadata {
  hobbyType: HobbyTypeDocument;
}

// 5. Course
export interface ICourseMetadata {
  courseType: CourseTypeDocument;
}

// 6. Exam
export interface IExamMetadata {
  exam: ExamDocument;
  examCategory: ExamCategoryDocument;
}

export type ICategoryMetadata = ISchoolMetadata | ICollegeMetadata | ILanguageMetadata | IHobbyMetadata | ICourseMetadata | IExamMetadata;

export interface ICategoryItemsResponse {
  subjects?: IEducationItem[];
  languages?: IEducationItem[];
  hobbies?: IEducationItem[];
  courses?: IEducationItem[];
  examSubjects?: IEducationItem[];
  metadata: ICategoryMetadata | null;
}

const normalizeString = (str: string): string => {
  if (!str || typeof str !== 'string') return '';
  return str
    .toLowerCase()
    .replace(/[\s\.\-]/g, '')
    .replace(/(exam|language|hobby|board|class|branch|degree|type|category|subject)s?$/i, '');
};

const classNumberRomanMap: Record<string, string> = {
  '1': 'I',
  '2': 'II',
  '3': 'III',
  '4': 'IV',
  '5': 'V',
  '6': 'VI',
  '7': 'VII',
  '8': 'VIII',
  '9': 'IX',
  '10': 'X',
  '11': 'XI',
  '12': 'XII',
};

const classRomanNumberMap: Record<string, string> = Object.fromEntries(Object.entries(classNumberRomanMap).map(([num, roman]) => [roman, num]));

function getClassSearchForms(searchTerm: string): string[] {
  const classNumberMatch = searchTerm.match(/^class\s*(\d{1,2})$/i);
  const classRomanMatch = searchTerm.match(/^class\s*([ivx]+)$/i);
  if (classNumberMatch) {
    const num = classNumberMatch[1];
    const roman = classNumberRomanMap[num];
    return roman ? [`Class ${num}`, `Class ${roman}`] : [`Class ${num}`];
  } else if (classRomanMatch) {
    const roman = classRomanMatch[1].toUpperCase();
    const num = classRomanNumberMap[roman];
    return num ? [`Class ${roman}`, `Class ${num}`] : [`Class ${roman}`];
  }
  return [];
}

/**
 * Search educational services based on query string
 * @route POST /api/v1/enquiries/search
 * @access Public
 */
export const searchEducationalServices = async (req: Request, res: Response): Promise<void> => {
  const validatedData = await validateData(searchQuerySchema, req.body);
  const { query } = validatedData;
  const searchTerm = query.trim();
  const normalizedSearchTerm = normalizeString(searchTerm);
  let matches: ISearchResponse['matches'] = [];

  // School: Class or Board
  const classSearchForms = getClassSearchForms(searchTerm);

  const schoolAgg: PipelineStage[] = [
    { $lookup: { from: 'boards', localField: 'board', foreignField: '_id', as: 'boardDetails' } },
    { $unwind: { path: '$boardDetails' } },
    {
      $match: {
        $or: [
          ...(classSearchForms.length > 0
            ? classSearchForms.map((form) => ({ name: { $regex: `^${form}$`, $options: 'i' } }))
            : [{ name: { $regex: searchTerm, $options: 'i' } }, { 'boardDetails.name': { $regex: searchTerm, $options: 'i' } }]),
        ],
      },
    },
    { $sort: { displayOrder: 1 } },
    {
      $project: {
        type: { $literal: 'schools' },
        details: {
          class: { id: '$_id', name: '$name' },
          board: { id: '$boardDetails._id', name: '$boardDetails.name' },
        },
        displayText: { $concat: ['$name', ' - ', '$boardDetails.name'] },
      },
    },
  ];
  matches = matches.concat(await Class.aggregate(schoolAgg));

  // College: Degree or Branch
  const collegeAgg = [
    {
      $lookup: {
        from: 'degrees',
        localField: 'degree',
        foreignField: '_id',
        as: 'degreeDetails',
      },
    },
    { $unwind: { path: '$degreeDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'degreelevels',
        localField: 'degreeDetails.degreeLevel',
        foreignField: '_id',
        as: 'degreeLevelDetails',
      },
    },
    { $unwind: { path: '$degreeLevelDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'streams',
        localField: 'degreeLevelDetails.stream',
        foreignField: '_id',
        as: 'streamDetails',
      },
    },
    { $unwind: { path: '$streamDetails', preserveNullAndEmptyArrays: true } },
    {
      $match: {
        $or: [
          { name: { $regex: normalizedSearchTerm, $options: 'i' } },
          { 'degreeDetails.name': { $regex: normalizedSearchTerm, $options: 'i' } },
          { 'degreeLevelDetails.name': { $regex: normalizedSearchTerm, $options: 'i' } },
          { 'streamDetails.name': { $regex: normalizedSearchTerm, $options: 'i' } },
        ],
      },
    },
    {
      $project: {
        type: { $literal: 'colleges' },
        details: {
          branch: { id: '$_id', name: '$name' },
          degree: { id: '$degreeDetails._id', name: '$degreeDetails.name' },
          degreeLevel: { id: '$degreeLevelDetails._id', name: '$degreeLevelDetails.name' },
          stream: { id: '$streamDetails._id', name: '$streamDetails.name' },
        },
        displayText: {
          $cond: [
            { $ifNull: ['$degreeDetails.name', false] },
            { $concat: ['$degreeDetails.name', ' - ', '$name'] },
            { $concat: ['', ' - ', '$name'] },
          ],
        },
      },
    },
  ];
  matches = matches.concat(await Branch.aggregate(collegeAgg));

  // Hobbies: Hobby Type or Hobby
  const hobbyAgg = [
    {
      $lookup: {
        from: 'hobbytypes',
        localField: 'hobbyType',
        foreignField: '_id',
        as: 'hobbyTypeDetails',
      },
    },
    { $unwind: { path: '$hobbyTypeDetails', preserveNullAndEmptyArrays: true } },
    {
      $match: {
        $or: [
          { name: { $regex: normalizedSearchTerm, $options: 'i' } },
          { 'hobbyTypeDetails.name': { $regex: normalizedSearchTerm, $options: 'i' } },
        ],
      },
    },
    {
      $project: {
        type: { $literal: 'hobbies' },
        details: {
          hobby: { id: '$_id', name: '$name' },
          hobbyType: { id: '$hobbyTypeDetails._id', name: '$hobbyTypeDetails.name' },
        },
        displayText: {
          $cond: [
            { $ifNull: ['$hobbyTypeDetails.name', false] },
            { $concat: ['$hobbyTypeDetails.name', ' - ', '$name'] },
            { $concat: ['', ' - ', '$name'] },
          ],
        },
      },
    },
  ];
  matches = matches.concat(await Hobby.aggregate(hobbyAgg));

  // Language: Language Family or Name
  const languageAgg = [
    {
      $lookup: {
        from: 'languagetypes',
        localField: 'languageType',
        foreignField: '_id',
        as: 'languageTypeDetails',
      },
    },
    { $unwind: { path: '$languageTypeDetails', preserveNullAndEmptyArrays: true } },
    {
      $match: {
        $or: [
          { name: { $regex: normalizedSearchTerm, $options: 'i' } },
          { 'languageTypeDetails.name': { $regex: normalizedSearchTerm, $options: 'i' } },
        ],
      },
    },
    {
      $project: {
        type: { $literal: 'languages' },
        details: {
          language: { id: '$_id', name: '$name' },
          languageType: { id: '$languageTypeDetails._id', name: '$languageTypeDetails.name' },
        },
        displayText: {
          $cond: [
            { $ifNull: ['$languageTypeDetails.name', false] },
            { $concat: ['$languageTypeDetails.name', ' - ', '$name'] },
            { $concat: ['', ' - ', '$name'] },
          ],
        },
      },
    },
  ];
  matches = matches.concat(await Language.aggregate(languageAgg));

  // Exam: Exam Name or Level
  const examAgg = [
    {
      $lookup: {
        from: 'examcategories',
        localField: 'examCategory',
        foreignField: '_id',
        as: 'examCategoryDetails',
      },
    },
    { $unwind: { path: '$examCategoryDetails', preserveNullAndEmptyArrays: true } },
    {
      $match: {
        $or: [
          { name: { $regex: normalizedSearchTerm, $options: 'i' } },
          { 'examCategoryDetails.name': { $regex: normalizedSearchTerm, $options: 'i' } },
        ],
      },
    },
    {
      $project: {
        type: { $literal: 'exams' },
        details: {
          exam: { id: '$_id', name: '$name' },
          examCategory: { id: '$examCategoryDetails._id', name: '$examCategoryDetails.name' },
        },
        displayText: {
          $cond: [
            { $ifNull: ['$examCategoryDetails.name', false] },
            { $concat: ['$examCategoryDetails.name', ' - ', '$name'] },
            { $concat: ['', ' - ', '$name'] },
          ],
        },
      },
    },
  ];
  matches = matches.concat(await Exam.aggregate(examAgg));

  // Course: Course Name or Type
  const courseAgg = [
    {
      $lookup: {
        from: 'coursetypes',
        localField: 'courseType',
        foreignField: '_id',
        as: 'courseTypeDetails',
      },
    },
    { $unwind: { path: '$courseTypeDetails', preserveNullAndEmptyArrays: true } },
    {
      $match: {
        $or: [
          { name: { $regex: searchTerm, $options: 'i' } },
          { name: { $regex: normalizedSearchTerm, $options: 'i' } },
          { 'courseTypeDetails.name': { $regex: searchTerm, $options: 'i' } },
          { 'courseTypeDetails.name': { $regex: normalizedSearchTerm, $options: 'i' } },
        ],
      },
    },
    {
      $project: {
        type: { $literal: 'it_courses' },
        details: {
          course: { id: '$_id', name: '$name' },
          courseCategory: { id: '$courseTypeDetails._id', name: '$courseTypeDetails.name' },
        },
        displayText: {
          $cond: [
            { $ifNull: ['$courseTypeDetails.name', false] },
            { $concat: ['$courseTypeDetails.name', ' - ', '$name'] },
            { $concat: ['', ' - ', '$name'] },
          ],
        },
      },
    },
  ];
  matches = matches.concat(await Course.aggregate(courseAgg));

  // Remove duplicates by displayText
  const uniqueMatches = Array.from(new Map(matches.map((item) => [item.displayText, item])).values());

  const data: ISearchResponse = { matches: uniqueMatches };

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Search results fetched successfully',
    data,
  });
};

/**
 * Get items by category type
 * @route POST /api/v1/enquiries/category:type
 * @access Public
 */

export const getCategoryItems = async (req: Request, res: Response): Promise<void> => {
  const type = req.params?.type as IServiceCategoryMap;

  if (!type || !Object.keys(serviceCategoryMap).includes(type)) {
    throw new BadRequestError('Please provide a valid category type');
  }

  const { id } = req.query;

  if (!id || !isValidObjectId(id as string)) {
    throw new BadRequestError('Please provide a valid ID');
  }

  let data: ICategoryItemsResponse = {
    metadata: null,
  };
  const objectId = new mongoose.Types.ObjectId(id as string);

  switch (type) {
    case 'schools':
      // Get subjects for the given class ID
      const subjects = await Subject.aggregate([
        { $match: { class: objectId, isActive: true } },
        {
          $lookup: {
            from: 'classes',
            localField: 'class',
            foreignField: '_id',
            as: 'classDetails',
          },
        },
        {
          $lookup: {
            from: 'boards',
            localField: 'classDetails.board',
            foreignField: '_id',
            as: 'boardDetails',
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            isActive: 1,
            createdAt: 1,
            updatedAt: 1,
            classDetails: { $arrayElemAt: ['$classDetails', 0] },
            boardDetails: { $arrayElemAt: ['$boardDetails', 0] },
          },
        },
      ]);

      data = {
        subjects,
        metadata:
          subjects.length > 0
            ? {
                class: subjects[0].classDetails,
                board: subjects[0].boardDetails,
              }
            : null,
      };
      break;

    case 'colleges':
      // Get college subjects for the given branch ID
      const collegeSubjects = await CollegeSubject.aggregate([
        { $match: { branch: objectId, isActive: true } },
        {
          $lookup: {
            from: 'branches',
            localField: 'branch',
            foreignField: '_id',
            as: 'branchDetails',
          },
        },
        {
          $lookup: {
            from: 'degrees',
            localField: 'branchDetails.degree',
            foreignField: '_id',
            as: 'degreeDetails',
          },
        },
        {
          $lookup: {
            from: 'degreelevels',
            localField: 'degreeDetails.degreeLevel',
            foreignField: '_id',
            as: 'degreeLevelDetails',
          },
        },
        {
          $lookup: {
            from: 'streams',
            localField: 'degreeLevelDetails.stream',
            foreignField: '_id',
            as: 'streamDetails',
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            isActive: 1,
            createdAt: 1,
            updatedAt: 1,
            branchDetails: { $arrayElemAt: ['$branchDetails', 0] },
            degreeDetails: { $arrayElemAt: ['$degreeDetails', 0] },
            degreeLevelDetails: { $arrayElemAt: ['$degreeLevelDetails', 0] },
            streamDetails: { $arrayElemAt: ['$streamDetails', 0] },
          },
        },
      ]);

      data = {
        subjects: collegeSubjects.map((subject) => ({
          _id: subject._id,
          name: subject.name,
          isActive: subject.isActive,
          createdAt: subject.createdAt,
          updatedAt: subject.updatedAt,
        })),
        metadata:
          collegeSubjects.length > 0
            ? {
                branch: collegeSubjects[0].branchDetails,
                degree: collegeSubjects[0].degreeDetails,
                degreeLevel: collegeSubjects[0].degreeLevelDetails,
                stream: collegeSubjects[0].streamDetails,
              }
            : null,
      };
      break;

    case 'languages':
      // Get languages for the given language type ID
      const languages = await Language.aggregate([
        { $match: { languageType: objectId, isActive: true } },
        {
          $lookup: {
            from: 'languagetypes',
            localField: 'languageType',
            foreignField: '_id',
            as: 'languageTypeDetails',
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            isActive: 1,
            createdAt: 1,
            updatedAt: 1,
            languageTypeDetails: { $arrayElemAt: ['$languageTypeDetails', 0] },
          },
        },
      ]);

      data = {
        languages,
        metadata:
          languages.length > 0
            ? {
                languageType: languages[0].languageTypeDetails,
              }
            : null,
      };
      break;

    case 'hobbies':
      // Get hobbies for the given hobby type ID
      const hobbies = await Hobby.aggregate([
        { $match: { hobbyType: objectId, isActive: true } },
        {
          $lookup: {
            from: 'hobbytypes',
            localField: 'hobbyType',
            foreignField: '_id',
            as: 'hobbyTypeDetails',
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            isActive: 1,
            createdAt: 1,
            updatedAt: 1,
            hobbyTypeDetails: { $arrayElemAt: ['$hobbyTypeDetails', 0] },
          },
        },
      ]);

      data = {
        hobbies,
        metadata:
          hobbies.length > 0
            ? {
                hobbyType: hobbies[0].hobbyTypeDetails,
              }
            : null,
      };
      break;

    case 'it_courses':
      // Get courses for the given course type ID
      const courses = await Course.aggregate([
        { $match: { courseType: objectId, isActive: true } },
        {
          $lookup: {
            from: 'coursetypes',
            localField: 'courseType',
            foreignField: '_id',
            as: 'courseTypeDetails',
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            isActive: 1,
            createdAt: 1,
            updatedAt: 1,
            courseTypeDetails: { $arrayElemAt: ['$courseTypeDetails', 0] },
          },
        },
      ]);

      data = {
        courses,
        metadata:
          courses.length > 0
            ? {
                courseType: courses[0].courseTypeDetails,
              }
            : null,
      };
      break;

    case 'exams':
      // Get exam subjects for the given exam ID
      const examSubjects = await ExamSubject.aggregate([
        { $match: { exam: objectId, isActive: true } },
        {
          $lookup: {
            from: 'exams',
            localField: 'exam',
            foreignField: '_id',
            as: 'examDetails',
          },
        },
        {
          $lookup: {
            from: 'examcategories',
            localField: 'examDetails.examCategory',
            foreignField: '_id',
            as: 'examCategoryDetails',
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            isActive: 1,
            createdAt: 1,
            updatedAt: 1,
            examDetails: { $arrayElemAt: ['$examDetails', 0] },
            examCategoryDetails: { $arrayElemAt: ['$examCategoryDetails', 0] },
          },
        },
      ]);

      data = {
        examSubjects,
        metadata:
          examSubjects.length > 0
            ? {
                exam: examSubjects[0].examDetails,
                examCategory: examSubjects[0].examCategoryDetails,
              }
            : null,
      };
      break;

    default:
      throw new BadRequestError('Unsupported service category type');
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: `${type} data fetched successfully`,
    data,
  });
};

/**
 * Create a new enquiry
 * @route POST /api/v1/enquiries/create
 * @access Private
 */
export const createEnquiry = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;

  const validatedData = await validateData(createEnquirySchema, req.body);
  const { childProfileId, preferences, ...categoryData } = validatedData;

  // Verify child profile belongs to user
  const childProfile = await ChildProfile.findOne({ _id: childProfileId, userId, isActive: true });

  if (!childProfile) {
    throw new NotFoundError(`No child profile found with id: ${childProfileId}`);
  }

  // Create the enquiry
  const enquiry = await Enquiry.create({ user: userId, childProfile: childProfileId, ...categoryData, ...preferences, status: 'pending' });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Enquiry created successfully',
    data: {
      enquiryId: enquiry._id,
      status: enquiry.status,
    },
  });
};
