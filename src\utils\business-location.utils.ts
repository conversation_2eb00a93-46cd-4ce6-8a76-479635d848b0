import BusinessLocation from '@/models/business-location.model';
import { IBusinessLocationTypesMap } from '@/validation/schemas/other/index.maps';

interface IBusinessLocation {
  name: string;
  location: string;
  type: IBusinessLocationTypesMap;
  coordinates?: { lng: number; lat: number };
}

export const handleBusinessLocation = async ({ name: businessName, location, type, coordinates }: IBusinessLocation): Promise<string> => {
  const normalizedName = businessName.trim();
  const normalizedLocation = location.trim();

  const updateObj: IBusinessLocation = { name: normalizedName, location: normalizedLocation, type };

  if (
    coordinates &&
    typeof coordinates.lng === 'number' &&
    typeof coordinates.lat === 'number' &&
    !isNaN(coordinates.lng) &&
    !isNaN(coordinates.lat)
  ) {
    updateObj.coordinates = { lat: coordinates.lat, lng: coordinates.lng };
  }

  const result = await BusinessLocation.findOneAndUpdate(
    {
      name: { $regex: `^${normalizedName}$`, $options: 'i' },
      location: { $regex: `^${normalizedLocation}$`, $options: 'i' },
      type,
      isActive: true,
    },
    { $set: updateObj },
    { new: true, upsert: true }
  );

  return result._id.toString();
};

export const getBusinessLocationType = (tuitionType: string): IBusinessLocationTypesMap => {
  switch (tuitionType) {
    case 'school':
      return 'school';
    case 'college':
      return 'college';
    case 'institute':
      return 'institute';
    default:
      return 'other';
  }
};
