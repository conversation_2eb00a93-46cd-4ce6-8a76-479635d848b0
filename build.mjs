import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function runCommand(command) {
  try {
    const { stdout, stderr } = await execAsync(command, { maxBuffer: 1024 * 1024 * 10 });
    if (stdout) console.log(stdout);
    if (stderr) console.error(stderr);
    return { stdout, stderr };
  } catch (error) {
    if (error.stdout) console.log(error.stdout);
    if (error.stderr) console.error(error.stderr);
    throw error;
  }
}

async function build() {
  const { default: chalk } = await import('chalk');

  console.log(chalk.blue('Starting build process...'));

  try {
    console.log(chalk.blue('Running tsc...'));
    await runCommand('npx tsc --pretty --listFiles --diagnostics');

    console.log(chalk.blue('Running tsc-alias...'));
    await runCommand('npx tsc-alias');

    console.log(chalk.green('Build completed successfully!'));
  } catch (error) {
    console.error(chalk.red('Build failed with error:'));
    console.error(chalk.red(error.message));
    process.exit(1);
  }
}

build();
