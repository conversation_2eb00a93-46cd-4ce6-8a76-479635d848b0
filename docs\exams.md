# Exams Service Category Documentation

## Overview

The platform includes two exam-related service categories: Competitive Exams and Entrance Exams. These categories provide a structured way to organize exam preparation content and services.

## Hierarchy

Both exam categories follow a three-level hierarchy:

1. **Exam Category** - The broad category of exams (e.g., Engineering, Medical, Civil Services)
2. **Exam** - The specific exam within a category (e.g., JEE, NEET, UPSC)
3. **Exam Subject** - The subjects or sections within an exam (e.g., Physics, Chemistry, General Studies)

```
Exams (Competitive/Entrance)
├── Exam Category (Engineering, Medical, Management, Law, Civil Services, etc.)
│   ├── Exam (JEE, NEET, CAT, CLAT, UPSC, etc.)
│   │   ├── Exam Subject (Physics, Chemistry, Mathematics, Biology, etc.)
```

## Data Models

### Exam Category Model

The Exam Category model represents broad categories of exams.

```typescript
// src/models/exam/exam-category.model.ts
export interface IExamCategory {
  name: IExamCategoryMap; // Enum: 'engineering', 'medical', etc.
  isActive: boolean;
}

// Schema definition
const examCategorySchema = new Schema<ExamCategoryDocument>({
  name: {
    type: String,
    enum: Object.keys(examCategoryMap),
    required: [true, 'Exam category name is required'],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
examCategorySchema.index({ name: 1 });
```

### Exam Model

The Exam model represents specific exams within a category.

```typescript
// src/models/exam/exam.model.ts
export interface IExam {
  name: string;
  examCategory: mongoose.Types.ObjectId; // Reference to ExamCategory
  isActive: boolean;
}

// Schema definition
const examSchema = new Schema<ExamDocument>({
  name: {
    type: String,
    required: [true, 'Exam name is required'],
    trim: true,
  },
  examCategory: {
    type: Schema.Types.ObjectId,
    ref: 'ExamCategory',
    required: [true, 'Exam category is required'],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
examSchema.index({ examCategory: 1, name: 1 }, { unique: true });
```

### Exam Subject Model

The Exam Subject model represents subjects or sections within an exam.

```typescript
// src/models/exam/exam-subject.model.ts
export interface IExamSubject {
  name: string;
  exam: mongoose.Types.ObjectId; // Reference to Exam
  isActive: boolean;
}

// Schema definition
const examSubjectSchema = new Schema<ExamSubjectDocument>({
  name: {
    type: String,
    required: [true, 'Exam subject name is required'],
    trim: true,
  },
  exam: {
    type: Schema.Types.ObjectId,
    ref: 'Exam',
    required: [true, 'Exam is required'],
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

// Index
examSubjectSchema.index({ exam: 1, name: 1 }, { unique: true });
```

## API Endpoints

### Exam Category Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/exam-categories` | Get all exam categories | No |
| GET | `/api/v1/education/exam-categories/:id` | Get a specific exam category | No |
| POST | `/api/v1/education/exam-categories` | Create a new exam category | Yes (admin) |
| PATCH | `/api/v1/education/exam-categories/:id` | Update an exam category | Yes (admin) |
| DELETE | `/api/v1/education/exam-categories/:id` | Delete an exam category | Yes (super_admin) |

### Exam Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/exams` | Get all exams | No |
| GET | `/api/v1/education/exams/category/:examCategoryId` | Get exams by category | No |
| GET | `/api/v1/education/exams/:id` | Get a specific exam | No |
| POST | `/api/v1/education/exams` | Create a new exam | Yes (admin) |
| PATCH | `/api/v1/education/exams/:id` | Update an exam | Yes (admin) |
| DELETE | `/api/v1/education/exams/:id` | Delete an exam | Yes (super_admin) |

### Exam Subject Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/api/v1/education/exam-subjects` | Get all exam subjects | No |
| GET | `/api/v1/education/exam-subjects/exam/:examId` | Get subjects by exam | No |
| GET | `/api/v1/education/exam-subjects/:id` | Get a specific exam subject | No |
| POST | `/api/v1/education/exam-subjects` | Create a new exam subject | Yes (admin) |
| PATCH | `/api/v1/education/exam-subjects/:id` | Update an exam subject | Yes (admin) |
| DELETE | `/api/v1/education/exam-subjects/:id` | Delete an exam subject | Yes (super_admin) |

## Predefined Data

### Exam Categories

The system comes with predefined exam categories:

- Engineering
- Medical
- Management
- Law
- Civil Services
- Other

### Example Exams and Subjects

#### Engineering Exams
- JEE Main
  - Physics
  - Chemistry
  - Mathematics
- JEE Advanced
  - Physics
  - Chemistry
  - Mathematics
- GATE
  - Engineering Mathematics
  - Core Subject Paper

#### Medical Exams
- NEET
  - Physics
  - Chemistry
  - Biology
- AIIMS
  - Physics
  - Chemistry
  - Biology
  - General Knowledge

#### Management Exams
- CAT
  - Quantitative Ability
  - Verbal Ability
  - Data Interpretation
  - Logical Reasoning
- XAT
  - Verbal & Logical Ability
  - Decision Making
  - Quantitative Ability & Data Interpretation

#### Law Exams
- CLAT
  - English
  - General Knowledge
  - Legal Reasoning
  - Logical Reasoning
  - Mathematics
- AILET
  - English
  - General Knowledge
  - Legal Aptitude
  - Reasoning
  - Mathematics

#### Civil Services Exams
- UPSC CSE
  - General Studies
  - CSAT
  - Optional Subject

## Data Flow

1. **Creating an Exam Category**: Admin creates an exam category (e.g., Engineering)
2. **Creating an Exam**: Admin creates an exam within that category (e.g., JEE Main)
3. **Creating Exam Subjects**: Admin creates subjects for that exam (e.g., Physics, Chemistry, Mathematics)

## Relationships with Other Entities

- **Users**: Tutors can specify which exams they prepare students for
- **Students**: Students can express interest in specific exam preparation
- **Content**: Educational content can be categorized by exam and subject
- **Mock Tests**: Practice tests can be organized by exam and subject

## Implementation Notes

- The hierarchy is enforced through validation in the controllers
- When creating entities at lower levels, the parent entities must exist
- All entities have an `isActive` flag to enable/disable without deletion
- The platform can distinguish between competitive exams and entrance exams at the service category level
