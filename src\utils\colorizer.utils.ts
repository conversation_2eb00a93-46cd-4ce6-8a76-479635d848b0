const colorCodes = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',

  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',

  bgBlack: '\x1b[40m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m',
};

type Color = keyof typeof colorCodes;

const colorize = (text: string, color: Color, bright = false): string => {
  return `${bright ? colorCodes.bright : ''}${colorCodes[color]}${text}${colorCodes.reset}`;
};

export const colorizer = {
  log: (text: string) => console.log(text),
  error: (text: string) => console.error(colorize(text, 'red', true)),
  warn: (text: string) => console.warn(colorize(text, 'yellow', true)),
  info: (text: string) => console.info(colorize(text, 'blue')),
  success: (text: string) => console.log(colorize(text, 'green')),
};

export default colorizer;
