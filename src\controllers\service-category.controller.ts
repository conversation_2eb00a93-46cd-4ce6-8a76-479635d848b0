import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import ServiceCategory from '@/models/service-category.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { AuthenticatedStaffRequest } from '@/types/express';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import { createServiceCategorySchema, UpdateServiceCategoryInput } from '@/validation/schemas/service-category.schema';

const serviceCategoryAggregation: PipelineStage[] = [{ $sort: { name: 1 } }];

export const createServiceCategory = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createServiceCategorySchema, req.body);
  const { name, isActive } = validatedData;

  if (!serviceCategoryMap[name]) {
    throw new BadRequestError(`Invalid service category key: ${name}`);
  }

  const existingCategory = await ServiceCategory.findOne({ name });
  if (existingCategory) {
    throw new BadRequestError(`A service category with the key '${name}' already exists`);
  }

  const serviceCategory = await ServiceCategory.create({ name, isActive });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Service category created successfully',
    data: { serviceCategory },
  });
};

export const getAllServiceCategories = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const baseAggregation = [...serviceCategoryAggregation];

  const queryManager = new AggregateQueryManager({
    model: ServiceCategory,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [serviceCategories, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Service categories fetched successfully',
    data: { serviceCategories, pagination },
  });
};

export const getServiceCategoryById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid service category ID');
  }

  const [serviceCategory] = await ServiceCategory.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...serviceCategoryAggregation]);

  if (!serviceCategory) {
    throw new NotFoundError(`No service category found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Service category fetched successfully',
    data: { serviceCategory },
  });
};

export const updateServiceCategory = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid service category ID');
  }

  const payload: UpdateServiceCategoryInput = {};

  if (req.body.isActive !== undefined) {
    payload['isActive'] = req.body.isActive;
  }

  const validatedData = await validateData(createServiceCategorySchema.partial(), payload);

  const serviceCategory = await ServiceCategory.findById(id);

  if (!serviceCategory) {
    throw new NotFoundError(`No service category found with id: ${id}`);
  }

  if (validatedData.name && validatedData.name !== serviceCategory.name) {
    throw new BadRequestError('Service category name cannot be changed');
  }

  const updatedServiceCategory = await ServiceCategory.findByIdAndUpdate(id, validatedData, { new: true, runValidators: true });

  if (!updatedServiceCategory) {
    throw new NotFoundError(`No service category found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Service category updated successfully',
    data: { serviceCategory: updatedServiceCategory },
  });
};
