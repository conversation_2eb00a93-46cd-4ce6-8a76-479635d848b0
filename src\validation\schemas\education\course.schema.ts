import { z } from 'zod';
import { objectIdSchema } from '../common.schema';

// Course Type Schemas
export const createCourseTypeSchema = z.object({
  name: z.string({ required_error: 'Course type name is required' }).min(1, 'Course type name is required'),
  isActive: z.boolean().default(true),
});

export type CreateCourseTypeInput = z.infer<typeof createCourseTypeSchema>;
export type UpdateCourseTypeInput = Partial<CreateCourseTypeInput>;

// Course Schemas
export const createCourseSchema = z.object({
  name: z.string({ required_error: 'Course name is required' }).min(1, 'Course name is required'),
  courseType: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateCourseInput = z.infer<typeof createCourseSchema>;
export type UpdateCourseInput = Partial<CreateCourseInput>;