import { Request, Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError } from '@/errors';
import Board from '@/models/school/board.model';
import Class from '@/models/school/class.model';
import Subject from '@/models/school/subject.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createBoardSchema, UpdateBoardInput } from '@/validation/schemas/education/school.schema';
import { AuthenticatedStaffRequest } from '@/types/express';

const boardAggregation: PipelineStage[] = [{ $sort: { name: 1 } }];

export const createBoard = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const validatedData = await validateData(createBoardSchema, req.body);
  const { name } = validatedData;

  const existingBoard = await Board.findOne({ name });
  if (existingBoard) {
    throw new BadRequestError(`A board with the name '${name}' already exists`);
  }

  const board = await Board.create({
    ...validatedData,
  });

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Board created successfully',
    data: { board },
  });
};

export const getAllBoards = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const matchStage: any = {};

  const baseAggregation = [{ $match: matchStage }, ...boardAggregation];

  const queryManager = new AggregateQueryManager({
    model: Board,
    queryString,
    baseAggregation,
  })
    .filter()
    .paginate();

  const [boards, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Boards fetched successfully',
    data: { boards, pagination },
  });
};

export const getBoardById = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid board ID');
  }

  const [board] = await Board.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...boardAggregation]);

  if (!board) {
    throw new NotFoundError(`No board found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Board fetched successfully',
    data: { board },
  });
};

export const updateBoard = async (req: AuthenticatedStaffRequest, res: Response): Promise<void> => {
  const { id } = req.params;

  const payload: UpdateBoardInput = {};

  if (req.body.name) {
    payload['name'] = req.body.name;
  }

  if (req.body.isActive !== undefined) {
    payload['isActive'] = req.body.isActive;
  }

  const validatedData = await validateData(createBoardSchema.partial(), payload);

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid board ID');
  }

  if (validatedData.name) {
    const existingBoard = await Board.findOne({ name: validatedData.name, _id: { $ne: id } });
    if (existingBoard) {
      throw new BadRequestError(`A board with the name '${validatedData.name}' already exists`);
    }
  }

  const updatedBoard = await Board.findByIdAndUpdate(id, validatedData, { new: true, runValidators: true });

  if (!updatedBoard) {
    throw new NotFoundError(`No board found with id: ${id}`);
  }

  const [newBoard] = await Board.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }, ...boardAggregation]);

  if (!newBoard) {
    throw new NotFoundError(`No board found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Board updated successfully',
    data: { board: newBoard },
  });
};

export const deleteBoard = async (req: Request<{ id: string }>, res: Response): Promise<void> => {
  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid board ID');
  }

  const classesUsingBoard = await Class.countDocuments({ board: id });

  if (classesUsingBoard > 0) {
    throw new BadRequestError(`Cannot delete board as it is associated with ${classesUsingBoard} classes. Please delete those classes first.`);
  }

  const subjectsUsingBoard = await Subject.countDocuments({ board: id });

  if (subjectsUsingBoard > 0) {
    throw new BadRequestError(`Cannot delete board as it is associated with ${subjectsUsingBoard} subjects. Please delete those subjects first.`);
  }

  const board = await Board.findByIdAndDelete(id);

  if (!board) {
    throw new NotFoundError(`No board found with id: ${id}`);
  }

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Board deleted successfully',
  });
};
