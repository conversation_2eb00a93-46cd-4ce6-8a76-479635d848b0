import rateLimit from 'express-rate-limit';
import { StatusCodes } from '@/constants';

const rateLimitErrorResponse = (_req: any, res: any) => {
  res.status(StatusCodes.TOO_MANY_REQUESTS).json({
    success: false,
    status: 'fail',
    message: 'Rate limit exceeded. Please try again later.',
  });
};

// 1. General API rate limiter (1000 requests within 15 minutes)
const apiLimiterMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 1000,
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitErrorResponse,
});

// 2. Authentication rate limiter (3 requests within 5 minutes)
export const authLimiter = rateLimit({
  windowMs: 5 * 60 * 1000,
  max: 3,
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitErrorResponse,
  skipSuccessfulRequests: true,
});

// 3. Password reset rate limiter (3 requests within 1 hour)
export const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000,
  max: 3,
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitErrorResponse,
});

export default apiLimiterMiddleware;
