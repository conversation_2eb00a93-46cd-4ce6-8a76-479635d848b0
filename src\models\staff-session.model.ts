import mongoose, { Document, Schema } from 'mongoose';

export interface IStaffSession {
  sessionId: string;
  IP: string;
  userAgent: string;
  isValid: boolean;
  staff: mongoose.Schema.Types.ObjectId;
  expiresAt: Date;
}

interface StaffSessionDocument extends IStaffSession, Document {
  createdAt: Date;
  updatedAt: Date;
}

const staffSessionSchema = new Schema<StaffSessionDocument>(
  {
    sessionId: { type: String, required: true, unique: true },
    IP: { type: String, required: true },
    userAgent: { type: String, required: true },
    isValid: { type: Boolean, default: true },
    staff: { type: Schema.Types.ObjectId, ref: 'Staff', required: true },
    expiresAt: { type: Date, required: true },
  },
  { timestamps: true }
);

staffSessionSchema.index({ staff: 1, isValid: 1 });
staffSessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

const StaffSession = mongoose.models.StaffSession || mongoose.model<StaffSessionDocument>('StaffSession', staffSessionSchema);

export default StaffSession;
