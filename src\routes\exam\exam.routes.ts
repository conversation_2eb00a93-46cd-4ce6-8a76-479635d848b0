import express from 'express';
import {
  createExam,
  getAllExams,
  getExamsByCategory,
  getExamById,
  updateExam,
  deleteExam,
} from '@/controllers/exam/exam.controller';
import { authenticatedStaff, authorizeStaffRoles } from '@/middleware/staff-auth.middleware';

const router = express.Router();

// Public routes
router.get('/', getAllExams);
router.get('/category/:examCategoryId', getExamsByCategory);
router.get('/:id', getExamById);

// Admin-only routes
router.post('/', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], createExam);
router.patch('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin', 'admin')], updateExam);
router.delete('/:id', [authenticatedStaff, authorizeStaffRoles('super_admin')], deleteExam);

export default router;
