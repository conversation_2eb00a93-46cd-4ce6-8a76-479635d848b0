import mongoose, { Document, Schema } from 'mongoose';

export interface IMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface IAiChat {
  user: mongoose.Types.ObjectId;
  messages: IMessage[];
  isMedicalRelated: boolean;
}

export interface AiChatDocument extends IAiChat, Document {
  createdAt: Date;
  updatedAt: Date;
}

const messageSchema = new Schema<IMessage>(
  {
    role: {
      type: String,
      enum: ['user', 'assistant'],
      required: [true, 'Message role is required'],
    },
    content: {
      type: String,
      required: [true, 'Message content is required'],
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
  },
  { _id: false }
);

const aiChatSchema = new Schema<AiChatDocument>(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    messages: [messageSchema],
    isMedicalRelated: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
aiChatSchema.index({ user: 1, createdAt: -1 });

const AiChat = mongoose.models.AiChat || mongoose.model<AiChatDocument>('AiChat', aiChatSchema);

export default AiChat;
