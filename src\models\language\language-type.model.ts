import mongoose, { Document, Schema } from 'mongoose';
import { CreateLanguageTypeInput } from '@/validation/schemas/education/language.schema';

export interface ILanguageType extends CreateLanguageTypeInput {}

export interface LanguageTypeDocument extends ILanguageType, Document {
  createdAt: Date;
  updatedAt: Date;
}

const languageTypeSchema = new Schema<LanguageTypeDocument>(
  {
    name: {
      type: String,
      required: [true, 'Language type name is required'],
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
languageTypeSchema.index({ name: 1 });

const LanguageType = mongoose.models.LanguageType || mongoose.model<LanguageTypeDocument>('LanguageType', languageTypeSchema);

export default LanguageType;
