import { Response } from 'express';
import { StatusCodes } from '@/constants';
import { BadRequestError, NotFoundError, UnauthorizedError } from '@/errors';
import EducationDetail from '@/models/education-detail.model';
import ChildProfile from '@/models/child-profile.model';
import { validateData } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import { handleBusinessLocation } from '@/utils/business-location.utils';
import {
  createEducationDetailSchema,
  updateEducationDetailSchema,
  UpdateEducationDetailInput,
} from '@/validation/schemas/parent/education-detail.schema';
import { AuthenticatedRequest } from '@/types/express';
import { UploadedFile } from 'express-fileupload';
import mongoose, { isValidObjectId, PipelineStage } from 'mongoose';
import { createFileUploader } from '@/utils/upload.utils';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';
import { IBusinessLocationTypesMap } from '@/validation/schemas/other/index.maps';
import { IEducationTypeMap } from '@/validation/schemas/parent/education.maps';

const processBusinessLocation = async (validatedData: {
  educationType: IEducationTypeMap;
  schoolName?: string;
  collegeName?: string;
  certificateBy?: string;
  location: string;
  coordinates?: { lng: number; lat: number };
}): Promise<string> => {
  let businessName: string | undefined;
  let businessType: IBusinessLocationTypesMap | undefined;

  switch (validatedData.educationType) {
    case 'school':
      businessName = validatedData.schoolName;
      businessType = 'school';
      break;
    case 'degree':
      businessName = validatedData.collegeName;
      businessType = 'college';
      break;
    case 'other':
      businessName = validatedData.certificateBy;
      businessType = 'institute';
      break;
    default:
      businessType = 'other';
      break;
  }

  if (!businessName) {
    throw new BadRequestError('Business name is required when providing business location');
  }

  return await handleBusinessLocation({
    name: businessName,
    location: validatedData.location,
    type: businessType,
    coordinates: validatedData.coordinates,
  });
};

const uploadCertificate = async (file: UploadedFile, userId: string): Promise<string> => {
  const fileUploader = createFileUploader('image/pdf');

  const directory = `certificates/${userId}`;
  const fileName = `certificate-${Date.now()}`;

  return await fileUploader.uploadFile({ file, fileName, directory });
};

const deleteCertificate = async (filePath: string): Promise<void> => {
  try {
    const relativePath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
    const absolutePath = path.join(__dirname, '..', '..', 'public', relativePath);

    const fileExists = await promisify(fs.access)(absolutePath, fs.constants.F_OK)
      .then(() => true)
      .catch(() => false);

    if (fileExists) {
      await promisify(fs.unlink)(absolutePath);
    }
  } catch (error) {
    console.error('Error deleting certificate:', error);
  }
};

const educationDetailAggregation: PipelineStage[] = [
  // School-specific lookups
  {
    $lookup: {
      from: 'boards',
      localField: 'boardId',
      foreignField: '_id',
      as: 'boardDetails',
    },
  },
  { $unwind: { path: '$boardDetails', preserveNullAndEmptyArrays: true } },
  {
    $lookup: {
      from: 'classes',
      localField: 'classId',
      foreignField: '_id',
      as: 'classDetails',
    },
  },
  { $unwind: { path: '$classDetails', preserveNullAndEmptyArrays: true } },

  // Degree-specific lookups
  {
    $lookup: {
      from: 'streams',
      localField: 'streamId',
      foreignField: '_id',
      as: 'streamDetails',
    },
  },
  { $unwind: { path: '$streamDetails', preserveNullAndEmptyArrays: true } },
  {
    $lookup: {
      from: 'degreelevels',
      localField: 'degreeLevelId',
      foreignField: '_id',
      as: 'degreeLevelDetails',
    },
  },
  { $unwind: { path: '$degreeLevelDetails', preserveNullAndEmptyArrays: true } },
  {
    $lookup: {
      from: 'degrees',
      localField: 'degreeId',
      foreignField: '_id',
      as: 'degreeDetails',
    },
  },
  { $unwind: { path: '$degreeDetails', preserveNullAndEmptyArrays: true } },
  {
    $lookup: {
      from: 'branches',
      localField: 'branchId',
      foreignField: '_id',
      as: 'branchDetails',
    },
  },
  { $unwind: { path: '$branchDetails', preserveNullAndEmptyArrays: true } },

  // Child profile lookup (for students)
  {
    $lookup: {
      from: 'childprofiles',
      localField: 'childProfileId',
      foreignField: '_id',
      as: 'childProfileDetails',
    },
  },
  { $unwind: { path: '$childProfileDetails', preserveNullAndEmptyArrays: true } },

  // Business location lookup
  {
    $lookup: {
      from: 'businesslocations',
      localField: 'businessLocationId',
      foreignField: '_id',
      as: 'businessLocationDetails',
    },
  },
  { $unwind: { path: '$businessLocationDetails', preserveNullAndEmptyArrays: true } },

  { $sort: { createdAt: -1 } },
];

export const createEducationDetail = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const userType = req.user?.userType;

  req.body.coordinates = req.body?.coordinates ? JSON.parse(req.body.coordinates) : undefined;
  req.body.isPursuing = ['true', true].includes(req.body?.isPursuing);

  const validatedData = await validateData(createEducationDetailSchema, req.body);
  const { type, childProfileId } = validatedData;

  const educationDetailData: any = { ...validatedData };

  // Always set userId (parent for students, tutor for tutors)
  educationDetailData.userId = userId;

  // Validate based on type
  if (type === 'student') {
    if (!childProfileId || !isValidObjectId(childProfileId)) {
      throw new BadRequestError('Please provide a valid child profile ID for student education records');
    }

    const childProfile = await ChildProfile.findOne({ _id: childProfileId, userId, isActive: true });

    if (!childProfile) throw new NotFoundError(`No active child profile found with id: ${childProfileId}`);
  } else if (type === 'tutor') {
    if (userType !== 'tutor') throw new UnauthorizedError('Only tutors can create tutor education records');
  }

  if (educationDetailData.educationType) {
    educationDetailData.businessLocationId = await processBusinessLocation({ ...educationDetailData });
  }

  if (req.files && req.files.attachmentUrl) {
    try {
      const certificateFile = req.files.attachmentUrl as UploadedFile;

      if (!certificateFile.mimetype.startsWith('image/') && certificateFile.mimetype !== 'application/pdf') {
        throw new BadRequestError('File must be a valid image or PDF');
      }

      const attachmentUrl = await uploadCertificate(certificateFile, userId as string);
      educationDetailData.attachmentUrl = attachmentUrl;
    } catch (error) {
      if (error instanceof BadRequestError) {
        throw error;
      }
      throw new BadRequestError('Error uploading certificate file');
    }
  }

  const newDetail = await EducationDetail.create(educationDetailData);

  const [educationDetail] = await EducationDetail.aggregate([{ $match: { _id: newDetail._id } }, ...educationDetailAggregation]);

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Education detail created successfully',
    data: { educationDetail },
  });
};

export const getEducationDetails = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const userType = req.user?.userType;
  const queryString = req.query;

  const type = queryString.type as string;
  const childProfileId = queryString.childProfileId as string;

  let baseMatchCondition: any = { isActive: true };

  if (type === 'student') {
    if (!childProfileId || !isValidObjectId(childProfileId)) {
      throw new BadRequestError('Please provide a valid child profile ID for student education records');
    }

    const childProfile = await ChildProfile.findOne({ _id: childProfileId, userId, isActive: true });
    if (!childProfile) throw new NotFoundError(`No active child profile found with id: ${childProfileId}`);

    baseMatchCondition = {
      type: 'student',
      userId: new mongoose.Types.ObjectId(userId),
      childProfileId: new mongoose.Types.ObjectId(childProfileId),
      isActive: true,
    };
  } else if (type === 'tutor') {
    if (userType !== 'tutor') throw new UnauthorizedError('Only tutors can access tutor education records');
    baseMatchCondition = { type: 'tutor', userId: new mongoose.Types.ObjectId(userId), isActive: true };
  } else {
    throw new BadRequestError('Please specify type as either "student" or "tutor"');
  }

  delete queryString?.type;
  delete queryString?.childProfileId;

  const baseAggregation = [{ $match: baseMatchCondition }, ...educationDetailAggregation];

  const queryManager = new AggregateQueryManager({ model: EducationDetail, queryString, baseAggregation }).filter().sort().paginate();

  const [educationDetails, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({ success: true, message: 'Education details fetched successfully', data: { educationDetails, pagination } });
};

export const getAllUserEducationDetails = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const queryString = req.query;

  // Get all education details for the logged-in user (both student and tutor records)
  const baseMatchCondition = {
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true,
  };

  const baseAggregation = [{ $match: baseMatchCondition }, ...educationDetailAggregation];

  const queryManager = new AggregateQueryManager({ model: EducationDetail, queryString, baseAggregation }).filter().sort().paginate();

  const [educationDetails, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'All user education details fetched successfully',
    data: { educationDetails, pagination },
  });
};

export const getEducationDetailById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const userType = req.user?.userType;

  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid education detail ID');
  }

  const [educationDetail] = await EducationDetail.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id), isActive: true } },
    ...educationDetailAggregation,
  ]);

  if (!educationDetail) {
    throw new NotFoundError(`No education detail found with id: ${id}`);
  }

  // Authorization check - userId should match for both types
  if (!educationDetail.userDetails || educationDetail.userDetails._id.toString() !== userId) {
    throw new UnauthorizedError('You are not authorized to access this education detail');
  }

  // Additional check for tutor type
  if (educationDetail.type === 'tutor' && userType !== 'tutor') {
    throw new UnauthorizedError('You are not authorized to access tutor education details');
  }

  res.status(StatusCodes.OK).json({ success: true, message: 'Education detail fetched successfully', data: { educationDetail } });
};

export const updateEducationDetail = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const userType = req.user?.userType;

  const { id } = req.params;

  req.body.coordinates = req.body?.coordinates ? JSON.parse(req.body.coordinates) : undefined;
  req.body.isPursuing = ['true', true].includes(req.body?.isPursuing);

  const fieldsToConvertEmptyToUndefined = ['obtainedValue', 'certificateNumber'];

  fieldsToConvertEmptyToUndefined.forEach((field) => {
    if (req.body[field] === '') {
      req.body[field] = undefined;
    }
  });

  const updateData = req.body as UpdateEducationDetailInput;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid education detail ID');
  }

  if (Object.keys(updateData).length === 0 && !req.files) {
    throw new BadRequestError('Please provide data to update');
  }

  const [educationDetail] = await EducationDetail.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id), isActive: true } },
    ...educationDetailAggregation,
  ]);

  if (!educationDetail) {
    throw new NotFoundError(`No education detail found with id: ${id}`);
  }

  // Authorization check - userId should match for both types
  if (!educationDetail.userDetails || educationDetail.userDetails._id.toString() !== userId) {
    throw new UnauthorizedError('You are not authorized to update this education detail');
  }

  // Additional check for tutor type
  if (educationDetail.type === 'tutor' && userType !== 'tutor') {
    throw new UnauthorizedError('You are not authorized to update tutor education details');
  }

  if (educationDetail.type === 'student' && updateData.childProfileId && updateData.childProfileId !== educationDetail.childProfileId.toString()) {
    if (!isValidObjectId(updateData.childProfileId)) {
      throw new BadRequestError('Please provide a valid child profile ID');
    }

    const newChildProfile = await ChildProfile.findOne({ _id: updateData.childProfileId, userId, isActive: true });

    if (!newChildProfile) {
      throw new NotFoundError(`No child profile found with id: ${updateData.childProfileId}`);
    }
  }

  let attachmentUrl: string | undefined;
  if (req.files && req.files.attachmentUrl) {
    try {
      const certificateFile = req.files.attachmentUrl as UploadedFile;

      if (!certificateFile.mimetype.startsWith('image/') && certificateFile.mimetype !== 'application/pdf') {
        throw new BadRequestError('File must be a valid image or PDF');
      }

      if (educationDetail.attachmentUrl) {
        await deleteCertificate(educationDetail.attachmentUrl);
      }

      attachmentUrl = await uploadCertificate(certificateFile, userId as string);
      updateData.attachmentUrl = attachmentUrl;
    } catch (error) {
      if (error instanceof BadRequestError) {
        throw error;
      }
      throw new BadRequestError('Error uploading certificate file');
    }
  }
  let validatedData: any;

  try {
    validatedData = await validateData(updateEducationDetailSchema, updateData);

    if (!validatedData.educationType) {
      validatedData.educationType = educationDetail.educationType;
    }

    if (
      validatedData.businessLocationId ||
      validatedData.educationType ||
      validatedData.schoolName ||
      validatedData.collegeName ||
      validatedData.certificateBy
    ) {
      if (!validatedData.location) {
        validatedData.location = educationDetail.location;
      }

      const coordinates = validatedData.coordinates || educationDetail.coordinates;

      validatedData.businessLocationId = await processBusinessLocation({ ...validatedData, coordinates });
    }
  } catch (error) {
    console.error('Error validating data:', error);
    throw new BadRequestError('Invalid data provided for update');
  }

  await EducationDetail.findByIdAndUpdate(id, validatedData, { runValidators: true });

  const [updatedEducationDetail] = await EducationDetail.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id) } },
    ...educationDetailAggregation,
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Education detail updated successfully',
    data: { educationDetail: updatedEducationDetail },
  });
};

export const deleteEducationDetail = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const userType = req.user?.userType;

  const { id } = req.params;

  if (!isValidObjectId(id)) {
    throw new BadRequestError('Please provide a valid education detail ID');
  }

  const [educationDetail] = await EducationDetail.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(id), isActive: true } },
    ...educationDetailAggregation,
  ]);

  if (!educationDetail) {
    throw new NotFoundError(`No education detail found with id: ${id}`);
  }

  // Authorization check - userId should match for both types
  if (!educationDetail.userDetails || educationDetail.userDetails._id.toString() !== userId) {
    throw new UnauthorizedError('You are not authorized to delete this education detail');
  }

  // Additional check for tutor type
  if (educationDetail.type === 'tutor' && userType !== 'tutor') {
    throw new UnauthorizedError('You are not authorized to delete tutor education details');
  }

  if (educationDetail.attachmentUrl) {
    await deleteCertificate(educationDetail.attachmentUrl);
  }

  await EducationDetail.findByIdAndUpdate(id, { isActive: false });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Education detail deleted successfully',
    data: null,
  });
};
